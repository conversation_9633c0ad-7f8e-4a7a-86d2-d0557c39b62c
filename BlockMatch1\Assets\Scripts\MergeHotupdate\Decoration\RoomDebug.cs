#if DEBUG || DEVELOPMENT_BUILD

using System.ComponentModel;
using DecorationRom.Event;
using DragonPlus.Core;
using DragonPlus.Save;
using Framework;
using TMGame;
using TMGame.Storage;

public partial class DebugOption
{
    [Category("装修")]
    [SROptions.DisplayName("参数1")]
    [SROptions.SortAttribute(1)]
    public int Common_Param1_room
    {
        get
        {
            return common_Param1_room;
        }
        set
        {
            common_Param1_room = value;
        }
    }
    private int common_Param1_room = 0;
    
    [Category("装修")]
    [SROptions.DisplayName("清档")]
    public void RoomClearStorage()
    {
        var roomCommon = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
        roomCommon.Clear();
        // Game.GetMgr<UIMgr>().Close(UIViewName.UIDebug);

    }
    
    [Category("装修")]
    [SROptions.DisplayName("完成当装修")]
    public void RoomComplete()
    {
        RoomManager.Instance.UnlockCurRoomAllNode();
        // Game.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_AreaTaskMain);
        //Game.Mod<ModEvent>().Dispatch<OnUnlockNextRoomEvent>(new OnUnlockNextRoomEvent());
        // Game.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
       EventBus.Dispatch(new OnUnlockNextRoomEvent());
    }

    [Category("装修")]
    [SROptions.DisplayName("完成装修，最后一个")]
    public void RoomCompleteWithLast()
    {
        RoomManager.Instance.UnlockAllWithoutLastOne();
        // Game.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_AreaTaskMain);
        //Game.Mod<ModEvent>().Dispatch<OnUnlockNextRoomEvent>(new OnUnlockNextRoomEvent());
        // Game.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
    }

    [Category("装修")]
    [SROptions.DisplayName("进入指定房间")]
    public void EnterRoom()
    {
        // Game.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
        int roomId = common_Param1_room;
        RoomManager.Instance.UnLockRoom(roomId);
        
    }
}

#endif
