[{"id": 101, "guideId": "GUIDE_101", "desc": "进入游戏时，直接切换到装修界面，引导点击清扫按钮", "triggerPosition": 1, "triggerParam": "0", "targetTypes": [1], "targetParams": ["0"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_101", "textGuideB": "guide_world_108", "NPCTextEnable": true, "NPCTextPos": 2, "fingerEnable": 1, "maskEnable": true, "maskColor": 0, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_CLICK_CLEAN_BUTTON", "bi_BGroup": "GAME_EVENT_FTE_TEST_CLICK_CLEAN_BUTTON"}, {"id": 102, "guideId": "GUIDE_102", "desc": "点对号确认装修", "triggerPosition": 2, "triggerParam": "GUIDE_101", "preGuideId": "GUIDE_101", "targetTypes": [2], "targetToTop": true, "saveFlag": true, "fingerEnable": 1, "maskEnable": true, "maskColor": 0, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_CONFIRM_CLEAN", "bi_BGroup": "GAME_EVENT_FTE_TEST_CONFIRM_CLEAN"}, {"id": 103, "guideId": "GUIDE_103", "desc": "装修界面点击返回主界面", "triggerPosition": 3, "triggerParam": "GUIDE_103", "preGuideId": "GUIDE_102", "targetTypes": [3], "targetToTop": true, "saveFlag": true, "textGuide": "guide_world_103", "NPCTextEnable": true, "NPCTextPos": 4, "fingerEnable": 1, "maskEnable": true, "maskColor": 0.6, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_CLICK_RETURN_BUTTON"}, {"id": 104, "guideId": "GUIDE_104", "desc": "引导点击“关卡模式”的按钮进入游戏（强制）", "triggerPosition": 4, "triggerParam": "1", "targetTypes": [4], "targetParams": ["1"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_104", "textGuideB": "guide_world_109", "NPCTextEnable": true, "NPCTextPos": 3, "fingerEnable": 1, "fingerPos": ["120", "-20"], "maskEnable": true, "maskColor": 0.6, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_CLICK_CLASSICGAME", "bi_BGroup": "GAME_EVENT_FTE_TEST_CLICK_CLASSICGAME"}, {"id": 106, "guideId": "GUIDE_106", "desc": "退出无限模式后，引导点击装修按钮（强制）", "triggerPosition": 6, "triggerParam": "1", "targetTypes": [6], "targetParams": ["1"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_106", "textGuideB": "guide_world_107", "NPCTextEnable": true, "NPCTextPos": 3, "fingerEnable": 1, "maskEnable": true, "maskColor": 0, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_CLICK_DECORATION_BUTTON", "bi_BGroup": "GAME_EVENT_FTE_TEST_CLICK_DECORATION_BUTTON"}, {"id": 201, "guideId": "GUIDE_201", "desc": "退出无限模式后，引导点击装修按钮（强制）", "triggerPosition": 6, "triggerParam": "2", "targetTypes": [6], "targetParams": ["1"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_107", "NPCTextEnable": true, "NPCTextPos": 3, "fingerEnable": 1, "maskEnable": true, "maskColor": 0, "clickToFinishType": 1}, {"id": 202, "guideId": "GUIDE_202", "desc": "引导点击清扫按钮", "triggerPosition": 1, "triggerParam": "0", "targetTypes": [1], "targetParams": ["0"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_108", "NPCTextEnable": true, "NPCTextPos": 2, "fingerEnable": 1, "maskEnable": true, "maskColor": 0, "clickToFinishType": 1}, {"id": 203, "guideId": "GUIDE_203", "desc": "点对号确认装修", "triggerPosition": 2, "triggerParam": "GUIDE_202", "preGuideId": "GUIDE_202", "targetTypes": [2], "targetToTop": true, "saveFlag": true, "fingerEnable": 1, "maskEnable": true, "maskColor": 0, "clickToFinishType": 1}, {"id": 204, "guideId": "GUIDE_204", "desc": "引导点击“无限模式”的按钮进入游戏（强制）", "triggerPosition": 4, "triggerParam": "1", "targetTypes": [4], "targetParams": ["1"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_109", "NPCTextEnable": true, "NPCTextPos": 3, "fingerEnable": 1, "maskEnable": true, "maskColor": 0.6, "clickToFinishType": 1}, {"id": 301, "guideId": "GUIDE_301", "desc": "转换方向道具引导", "triggerPosition": 7, "triggerParam": "1", "levelLimited": 2, "targetTypes": [7], "targetParams": ["1"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_1_30", "NPCTextEnable": true, "NPCTextPos": 3, "fingerEnable": 1, "fingerPos": ["0", "-20"], "maskEnable": true, "maskColor": 0.6, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_GUIDE_301", "bi_BGroup": "GAME_EVENT_FTE_GUIDE_301_2"}, {"id": 302, "guideId": "GUIDE_302", "desc": "弱引导点击道具进行转换方向", "triggerPosition": 2, "triggerParam": "GUIDE_301", "levelLimited": 2, "targetTypes": [8, 8, 8], "targetParams": ["0", "1", "2"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_1_31", "NPCTextEnable": true, "NPCTextPos": 4, "arrowEnable": [1, 1, 1], "arrowPos": [4, 4, 4], "maskEnable": true, "maskColor": 0.6, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_GUIDE_302", "bi_BGroup": "GAME_EVENT_FTE_GUIDE_302_2"}, {"id": 303, "guideId": "GUIDE_303", "desc": "炸弹道具引导", "triggerPosition": 8, "triggerParam": "1", "levelLimited": 5, "targetTypes": [9], "targetParams": ["1"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_1_32", "NPCTextEnable": true, "NPCTextPos": 3, "fingerEnable": 1, "fingerPos": ["0", "-20"], "maskEnable": true, "maskColor": 0.6, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_GUIDE_303", "bi_BGroup": "GAME_EVENT_FTE_GUIDE_303_2"}, {"id": 305, "guideId": "GUIDE_305", "desc": "变单块的道具引导", "triggerPosition": 9, "triggerParam": "1", "levelLimited": 8, "targetTypes": [11], "targetParams": ["1"], "targetToTop": true, "ignoreCache": true, "saveFlag": true, "textGuide": "guide_world_1_34", "NPCTextEnable": true, "NPCTextPos": 3, "fingerEnable": 1, "fingerPos": ["0", "-20"], "maskEnable": true, "maskColor": 0.6, "clickToFinishType": 1, "bi": "GAME_EVENT_FTE_GUIDE_305", "bi_BGroup": "GAME_EVENT_FTE_GUIDE_305_2"}]