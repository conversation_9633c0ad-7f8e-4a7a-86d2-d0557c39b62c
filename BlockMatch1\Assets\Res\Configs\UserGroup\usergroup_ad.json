{"gmeditconfig": [], "presetconfig": [{"presetName": "T1", "presetValue": "US", "desc": "第一梯队国家码"}, {"presetName": "T2", "presetValue": "KR,DE,AU,CA,JP,GB", "desc": "第二梯队国家码"}], "rules": [{"groupName": "origin", "groupId": 0, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "0", "desc": "默认兜底组"}, {"groupName": "非T1T2,生命周期＞10，免费用户", "groupId": 101, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期＞10，免费用户", "groupId": 101, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "10"}, {"groupName": "非T1T2,生命周期＞10，免费用户", "groupId": 101, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期＞10，免费用户", "groupId": 102, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期＞10，免费用户", "groupId": 102, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "10"}, {"groupName": "T2,生命周期＞10，免费用户", "groupId": 102, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期＞15，免费用户", "groupId": 103, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期＞15，免费用户", "groupId": 103, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "15"}, {"groupName": "T1,生命周期＞15，免费用户", "groupId": 103, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1000, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1000, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1000, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1000, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1001, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1001, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1001, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1001, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1001, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台", "groupId": 1002, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台", "groupId": 1002, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台", "groupId": 1002, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台", "groupId": 1002, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台", "groupId": 1002, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1010, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1010, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1010, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1011, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1011, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1011, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1011, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台", "groupId": 1012, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台", "groupId": 1012, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台", "groupId": 1012, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台", "groupId": 1012, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台", "groupId": 1012, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1100, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1100, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1100, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台", "groupId": 1100, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1101, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1101, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1101, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1101, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台", "groupId": 1101, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台", "groupId": 1102, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台", "groupId": 1102, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台", "groupId": 1102, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台", "groupId": 1102, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台", "groupId": 1102, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台", "groupId": 1103, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台", "groupId": 1103, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台", "groupId": 1103, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台", "groupId": 1103, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台", "groupId": 1103, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1110, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1110, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1110, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台", "groupId": 1110, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1111, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1111, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1111, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1111, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台", "groupId": 1111, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台", "groupId": 1112, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台", "groupId": 1112, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台", "groupId": 1112, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台", "groupId": 1112, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台", "groupId": 1112, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台", "groupId": 1113, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台", "groupId": 1113, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台", "groupId": 1113, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "7"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台", "groupId": 1113, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台", "groupId": 1113, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台", "groupId": 1114, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台", "groupId": 1114, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "7"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台", "groupId": 1114, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台", "groupId": 1114, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台", "groupId": 1114, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台", "groupId": 1200, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台", "groupId": 1200, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台", "groupId": 1200, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台", "groupId": 1200, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台", "groupId": 1201, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台", "groupId": 1201, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台", "groupId": 1201, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台", "groupId": 1201, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台", "groupId": 1201, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台", "groupId": 1202, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台", "groupId": 1202, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台", "groupId": 1202, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台", "groupId": 1202, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台", "groupId": 1202, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台", "groupId": 1203, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台", "groupId": 1203, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台", "groupId": 1203, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台", "groupId": 1203, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台", "groupId": 1203, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台", "groupId": 1204, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台", "groupId": 1204, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台", "groupId": 1204, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台", "groupId": 1204, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台", "groupId": 1204, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(10,15]，免费用户，GP平台", "groupId": 1205, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(10,15]，免费用户，GP平台", "groupId": 1205, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(10,15]，免费用户，GP平台", "groupId": 1205, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "15"}, {"groupName": "T1,生命周期(10,15]，免费用户，GP平台", "groupId": 1205, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(10,15]，免费用户，GP平台", "groupId": 1205, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台", "groupId": 1210, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台", "groupId": 1210, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台", "groupId": 1210, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台", "groupId": 1210, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台", "groupId": 1211, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台", "groupId": 1211, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台", "groupId": 1211, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台", "groupId": 1211, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台", "groupId": 1211, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台", "groupId": 1212, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台", "groupId": 1212, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台", "groupId": 1212, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台", "groupId": 1212, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台", "groupId": 1212, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台", "groupId": 1213, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台", "groupId": 1213, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台", "groupId": 1213, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台", "groupId": 1213, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台", "groupId": 1213, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台", "groupId": 1214, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台", "groupId": 1214, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台", "groupId": 1214, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台", "groupId": 1214, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台", "groupId": 1214, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台", "groupId": 1215, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台", "groupId": 1215, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台", "groupId": 1215, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "12"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台", "groupId": 1215, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台", "groupId": 1215, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台", "groupId": 1216, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台", "groupId": 1216, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "12"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台", "groupId": 1216, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "15"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台", "groupId": 1216, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台", "groupId": 1216, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6000, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6000, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6000, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6000, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6000, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6001, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6001, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6001, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6001, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6001, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6001, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，GP平台，内购组", "groupId": 6002, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，GP平台，内购组", "groupId": 6002, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，GP平台，内购组", "groupId": 6002, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，GP平台，内购组", "groupId": 6002, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "6"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，GP平台，内购组", "groupId": 6002, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，GP平台，内购组", "groupId": 6002, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，GP平台，内购组", "groupId": 6003, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，GP平台，内购组", "groupId": 6003, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，GP平台，内购组", "groupId": 6003, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "6"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，GP平台，内购组", "groupId": 6003, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，GP平台，内购组", "groupId": 6003, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，GP平台，内购组", "groupId": 6003, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6010, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6010, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6010, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6010, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6011, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6011, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6011, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6011, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6011, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，IOS平台，内购组", "groupId": 6012, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，IOS平台，内购组", "groupId": 6012, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，IOS平台，内购组", "groupId": 6012, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，IOS平台，内购组", "groupId": 6012, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "6"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，IOS平台，内购组", "groupId": 6012, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(3,6]，免费用户，IOS平台，内购组", "groupId": 6012, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，IOS平台，内购组", "groupId": 6013, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，IOS平台，内购组", "groupId": 6013, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，IOS平台，内购组", "groupId": 6013, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "6"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，IOS平台，内购组", "groupId": 6013, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，IOS平台，内购组", "groupId": 6013, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(6,10]，免费用户，IOS平台，内购组", "groupId": 6013, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6100, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6100, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6100, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6100, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6100, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6101, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6101, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6101, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6101, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6101, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6101, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6102, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6102, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6102, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6102, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6102, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6102, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台，内购组", "groupId": 6103, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台，内购组", "groupId": 6103, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台，内购组", "groupId": 6103, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台，内购组", "groupId": 6103, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台，内购组", "groupId": 6103, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(5,10]，免费用户，GP平台，内购组", "groupId": 6103, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6110, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6110, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6110, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6110, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6110, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6111, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6111, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6111, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6111, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6111, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6111, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6112, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6112, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6112, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6112, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6112, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6112, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6113, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6113, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6113, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6113, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "7"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6113, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6113, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6114, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6114, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6114, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "7"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6114, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6114, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6114, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6200, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6200, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6200, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6200, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，内购组", "groupId": 6200, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6201, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6201, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6201, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6201, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6201, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，内购组", "groupId": 6201, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6202, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6202, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6202, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6202, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6202, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(3,5]，免费用户，GP平台，内购组", "groupId": 6202, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台，内购组", "groupId": 6203, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台，内购组", "groupId": 6203, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台，内购组", "groupId": 6203, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台，内购组", "groupId": 6203, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台，内购组", "groupId": 6203, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(5,7]，免费用户，GP平台，内购组", "groupId": 6203, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台，内购组", "groupId": 6204, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台，内购组", "groupId": 6204, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台，内购组", "groupId": 6204, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台，内购组", "groupId": 6204, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台，内购组", "groupId": 6204, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(7,10]，免费用户，GP平台，内购组", "groupId": 6204, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(10,12]，免费用户，GP平台，内购组", "groupId": 6205, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(10,12]，免费用户，GP平台，内购组", "groupId": 6205, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(10,12]，免费用户，GP平台，内购组", "groupId": 6205, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(10,12]，免费用户，GP平台，内购组", "groupId": 6205, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "12"}, {"groupName": "T1,生命周期(10,12]，免费用户，GP平台，内购组", "groupId": 6205, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(10,12]，免费用户，GP平台，内购组", "groupId": 6205, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(12,15]，免费用户，GP平台，内购组", "groupId": 6206, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(12,15]，免费用户，GP平台，内购组", "groupId": 6206, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(12,15]，免费用户，GP平台，内购组", "groupId": 6206, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "12"}, {"groupName": "T1,生命周期(12,15]，免费用户，GP平台，内购组", "groupId": 6206, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "15"}, {"groupName": "T1,生命周期(12,15]，免费用户，GP平台，内购组", "groupId": 6206, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(12,15]，免费用户，GP平台，内购组", "groupId": 6206, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6210, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6210, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6210, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6210, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，内购组", "groupId": 6210, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6211, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6211, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6211, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6211, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6211, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，内购组", "groupId": 6211, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6212, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6212, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6212, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6212, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6212, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，内购组", "groupId": 6212, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6213, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6213, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6213, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6213, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6213, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(5,7]，免费用户，IOS平台，内购组", "groupId": 6213, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6214, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6214, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6214, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "7"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6214, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6214, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(7,10]，免费用户，IOS平台，内购组", "groupId": 6214, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台，内购组", "groupId": 6215, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台，内购组", "groupId": 6215, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台，内购组", "groupId": 6215, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "10"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台，内购组", "groupId": 6215, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "12"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台，内购组", "groupId": 6215, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(10,12]，免费用户，IOS平台，内购组", "groupId": 6215, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台，内购组", "groupId": 6216, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "1", "desc": "市场买量定义为【内购组】"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台，内购组", "groupId": 6216, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台，内购组", "groupId": 6216, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "12"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台，内购组", "groupId": 6216, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "15"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台，内购组", "groupId": 6216, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(12,15]，免费用户，IOS平台，内购组", "groupId": 6216, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7000, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7000, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7000, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7000, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7000, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7001, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7001, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7001, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7001, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7001, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7001, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7002, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7002, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7002, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7002, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7002, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7002, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7010, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7010, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7010, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7010, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7011, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7011, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7011, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7011, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7011, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台，广告组", "groupId": 7012, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台，广告组", "groupId": 7012, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台，广告组", "groupId": 7012, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台，广告组", "groupId": 7012, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台，广告组", "groupId": 7012, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(3,10]，免费用户，IOS平台，广告组", "groupId": 7012, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7100, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7100, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7100, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7100, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7100, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7101, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7101, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7101, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7101, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7101, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7101, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7102, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7102, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7102, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7102, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7102, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(3,10]，免费用户，GP平台，广告组", "groupId": 7102, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7110, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7110, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7110, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7110, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7110, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7111, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7111, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7111, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7111, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7111, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7111, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7112, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7112, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7112, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7112, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7112, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7112, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(5,10]，免费用户，IOS平台，广告组", "groupId": 7113, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T2,生命周期(5,10]，免费用户，IOS平台，广告组", "groupId": 7113, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(5,10]，免费用户，IOS平台，广告组", "groupId": 7113, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T2,生命周期(5,10]，免费用户，IOS平台，广告组", "groupId": 7113, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "10"}, {"groupName": "T2,生命周期(5,10]，免费用户，IOS平台，广告组", "groupId": 7113, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(5,10]，免费用户，IOS平台，广告组", "groupId": 7113, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7200, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7200, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7200, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7200, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期[0,1]，免费用户，GP平台，广告组", "groupId": 7200, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7201, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7201, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7201, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7201, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7201, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(1,3]，免费用户，GP平台，广告组", "groupId": 7201, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(3,15]，免费用户，GP平台，广告组", "groupId": 7202, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T1,生命周期(3,15]，免费用户，GP平台，广告组", "groupId": 7202, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(3,15]，免费用户，GP平台，广告组", "groupId": 7202, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(3,15]，免费用户，GP平台，广告组", "groupId": 7202, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "15"}, {"groupName": "T1,生命周期(3,15]，免费用户，GP平台，广告组", "groupId": 7202, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(3,15]，免费用户，GP平台，广告组", "groupId": 7202, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7210, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7210, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7210, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7210, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期[0,1]，免费用户，IOS平台，广告组", "groupId": 7210, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7211, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7211, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7211, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "1"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7211, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7211, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(1,3]，免费用户，IOS平台，广告组", "groupId": 7211, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7212, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7212, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7212, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "3"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7212, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7212, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(3,5]，免费用户，IOS平台，广告组", "groupId": 7212, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(5,15]，免费用户，IOS平台，广告组", "groupId": 7213, "dataKey": "sourceCampaign", "operator": "eq", "dataType": "typecode", "dataValue": "2", "desc": "市场买量定义为【广告组】"}, {"groupName": "T1,生命周期(5,15]，免费用户，IOS平台，广告组", "groupId": 7213, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(5,15]，免费用户，IOS平台，广告组", "groupId": 7213, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "5"}, {"groupName": "T1,生命周期(5,15]，免费用户，IOS平台，广告组", "groupId": 7213, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "15"}, {"groupName": "T1,生命周期(5,15]，免费用户，IOS平台，广告组", "groupId": 7213, "dataKey": "iapTotal", "operator": "eq", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(5,15]，免费用户，IOS平台，广告组", "groupId": 7213, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，GP平台", "groupId": 2010, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，GP平台", "groupId": 2010, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，GP平台", "groupId": 2010, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，GP平台", "groupId": 2010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，IOS平台", "groupId": 2011, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，IOS平台", "groupId": 2011, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，IOS平台", "groupId": 2011, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，IOS平台", "groupId": 2011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，GP平台", "groupId": 2020, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，GP平台", "groupId": 2020, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，GP平台", "groupId": 2020, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，GP平台", "groupId": 2020, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，IOS平台", "groupId": 2021, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，IOS平台", "groupId": 2021, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，IOS平台", "groupId": 2021, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，IOS平台", "groupId": 2021, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，GP平台", "groupId": 2030, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，GP平台", "groupId": 2030, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，GP平台", "groupId": 2030, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，GP平台", "groupId": 2030, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，IOS平台", "groupId": 2031, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，IOS平台", "groupId": 2031, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，IOS平台", "groupId": 2031, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，IOS平台", "groupId": 2031, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "生命周期(20,∞)，$20≤单次付费＜$45", "groupId": 2040, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，$20≤单次付费＜$45", "groupId": 2040, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "生命周期(20,∞)，$20≤单次付费＜$45", "groupId": 2040, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "4500"}, {"groupName": "生命周期(20,∞)，$45≤单次付费＜$70", "groupId": 2050, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，$45≤单次付费＜$70", "groupId": 2050, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "4500"}, {"groupName": "生命周期(20,∞)，$45≤单次付费＜$70", "groupId": 2050, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "生命周期(20,∞)，单次付费≥$70", "groupId": 2060, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，单次付费≥$70", "groupId": 2060, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 3010, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 3010, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 3010, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 3010, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 3010, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 3010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 3011, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 3011, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 3011, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 3011, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 3011, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 3011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 3012, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 3012, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 3012, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 3012, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 3012, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 3020, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 3020, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 3020, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 3020, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 3020, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 3020, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 3021, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 3021, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 3021, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 3021, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 3021, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 3021, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 3030, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 3030, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 3030, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 3030, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 3030, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 3030, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 3040, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 3040, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 3040, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 3040, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "非T1T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 3040, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 3101, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 3101, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 3101, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 3101, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 3101, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 3102, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 3102, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 3102, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 3102, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 3102, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 3102, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 3103, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 3103, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 3103, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 3103, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "非T1T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 3103, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 3201, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 3201, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 3201, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 3201, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 3201, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 3202, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 3202, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 3202, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 3202, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 3202, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 3202, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 3203, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 3203, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 3203, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 3203, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "非T1T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 3203, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 3301, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 3301, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 3301, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 3301, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 3301, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 3302, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 3302, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 3302, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 3302, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 3302, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 3302, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 3303, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 3303, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 3303, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 3303, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "非T1T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 3303, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 3401, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 3401, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 3401, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 3401, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "9000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 3401, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 3402, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 3402, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 3402, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 3402, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 3402, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "9000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 3402, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 3403, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 3403, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 3403, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 3403, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "非T1T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 3403, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 3501, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 3501, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 3501, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 3501, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 3502, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 3502, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 3502, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "非T1T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 3502, "dataKey": "country", "operator": "nin", "dataType": "arraystring", "dataValue": "T1,T2"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 4010, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 4010, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 4010, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 4010, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 4010, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 4010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 4011, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 4011, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 4011, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 4011, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 4011, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 4011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 4012, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 4012, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 4012, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 4012, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 4012, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 4020, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 4020, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 4020, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 4020, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 4020, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 4020, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 4021, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 4021, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 4021, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 4021, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 4021, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 4021, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 4022, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 4022, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 4022, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 4022, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 4022, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 4022, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 4023, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 4023, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 4023, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 4023, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "T2,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 4023, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 4030, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 4030, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 4030, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 4030, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 4030, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 4031, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 4031, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 4031, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 4031, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 4031, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 4031, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 4032, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 4032, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 4032, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 4032, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "T2,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 4032, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 4040, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 4040, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 4040, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 4040, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 4040, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 4041, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 4041, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 4041, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 4041, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 4041, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 4041, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 4042, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 4042, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 4042, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 4042, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "T2,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 4042, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 4050, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 4050, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 4050, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 4050, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 4050, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 4051, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 4051, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 4051, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 4051, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 4051, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 4051, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 4052, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 4052, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 4052, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 4052, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "T2,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 4052, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 4060, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 4060, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 4060, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 4060, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "9000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 4060, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 4061, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 4061, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 4061, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 4061, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 4061, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "9000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 4061, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 4062, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 4062, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 4062, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 4062, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "T2,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 4062, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 4070, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 4070, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 4070, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 4070, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 4071, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 4071, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 4071, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "T2,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 4071, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T2"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 5010, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 5010, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 5010, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 5010, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 5010, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，GP平台", "groupId": 5010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 5011, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 5011, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 5011, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 5011, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 5011, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费＜20，IOS平台", "groupId": 5011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 5012, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 5012, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 5012, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "500"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 5012, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，0＜单次付费＜$5，累计付费≥20", "groupId": 5012, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 5020, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 5020, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 5020, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 5020, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 5020, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，GP平台", "groupId": 5020, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 5021, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 5021, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 5021, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 5021, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 5021, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费＜50，IOS平台", "groupId": 5021, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 5022, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 5022, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 5022, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 5022, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 5022, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，50≤累计付费＜100", "groupId": 5022, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 5023, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 5023, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "500"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 5023, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 5023, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "T1,生命周期(0,20]，5≤单次付费＜$10，累计付费≥100", "groupId": 5023, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 5030, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 5030, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 5030, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 5030, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费＜50", "groupId": 5030, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 5031, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 5031, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 5031, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 5031, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 5031, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，50≤累计付费＜100", "groupId": 5031, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 5032, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 5032, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 5032, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 5032, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "T1,生命周期(0,20]，10≤单次付费＜$20，累计付费≥100", "groupId": 5032, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 5040, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 5040, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 5040, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 5040, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费＜50", "groupId": 5040, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 5041, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 5041, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 5041, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 5041, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "10000"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 5041, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，50≤累计付费＜100", "groupId": 5041, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 5042, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 5042, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "3500"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 5042, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 5042, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "10000"}, {"groupName": "T1,生命周期(0,20]，20≤单次付费＜$35，累计付费≥100", "groupId": 5042, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 5050, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 5050, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 5050, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 5050, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费＜70", "groupId": 5050, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 5051, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 5051, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 5051, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 5051, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 5051, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，70≤累计付费＜120", "groupId": 5051, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 5052, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 5052, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 5052, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "3500"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 5052, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "T1,生命周期(0,20]，35≤单次付费＜$50，累计付费≥120", "groupId": 5052, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 5060, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 5060, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 5060, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 5060, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "9000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费＜90", "groupId": 5060, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 5061, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 5061, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 5061, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 5061, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 5061, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "9000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，90≤累计付费＜120", "groupId": 5061, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 5062, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 5062, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 5062, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "5000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 5062, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "T1,生命周期(0,20]，50≤单次付费＜$70，累计付费≥120", "groupId": 5062, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 5070, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 5070, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 5070, "dataKey": "iapTotal", "operator": "lt", "dataType": "number", "dataValue": "12000"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费＜120", "groupId": 5070, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 5071, "dataKey": "installedAt", "operator": "lte", "dataType": "days", "dataValue": "20"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 5071, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 5071, "dataKey": "iapTotal", "operator": "gte", "dataType": "number", "dataValue": "12000"}, {"groupName": "T1,生命周期(0,20]，单次付费≥$70，累计付费≥120", "groupId": 5071, "dataKey": "country", "operator": "in", "dataType": "arraystring", "dataValue": "T1"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，GP平台", "groupId": 9000, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，GP平台", "groupId": 9000, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，GP平台", "groupId": 9000, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，GP平台", "groupId": 9000, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "8"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，GP平台", "groupId": 9000, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，IOS平台", "groupId": 9001, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，IOS平台", "groupId": 9001, "dataKey": "iap_single_max", "operator": "gt", "dataType": "number", "dataValue": "0"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，IOS平台", "groupId": 9001, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，IOS平台", "groupId": 9001, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "8"}, {"groupName": "生命周期(20,∞)，0＜单次付费＜$4，距上次付费＞7日，IOS平台", "groupId": 9001, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，GP平台", "groupId": 9010, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，GP平台", "groupId": 9010, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，GP平台", "groupId": 9010, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，GP平台", "groupId": 9010, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "8"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，GP平台", "groupId": 9010, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "android"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，IOS平台", "groupId": 9011, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，IOS平台", "groupId": 9011, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "400"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，IOS平台", "groupId": 9011, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "1000"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，IOS平台", "groupId": 9011, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "8"}, {"groupName": "生命周期(20,∞)，4≤单次付费＜$10，距上次付费＞7日，IOS平台", "groupId": 9011, "dataKey": "platform", "operator": "eq", "dataType": "string", "dataValue": "ios"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，距上次付费＞7日", "groupId": 9020, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，距上次付费＞7日", "groupId": 9020, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "1000"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，距上次付费＞7日", "groupId": 9020, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "2000"}, {"groupName": "生命周期(20,∞)，$10≤单次付费＜$20，距上次付费＞7日", "groupId": 9020, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "8"}, {"groupName": "生命周期(20,∞)，$20≤单次付费＜$45，距上次付费＞10日", "groupId": 9030, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，$20≤单次付费＜$45，距上次付费＞10日", "groupId": 9030, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "2000"}, {"groupName": "生命周期(20,∞)，$20≤单次付费＜$45，距上次付费＞10日", "groupId": 9030, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "4500"}, {"groupName": "生命周期(20,∞)，$20≤单次付费＜$45，距上次付费＞10日", "groupId": 9030, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "11"}, {"groupName": "生命周期(20,∞)，$45≤单次付费＜$70，距上次付费＞10日", "groupId": 9040, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，$45≤单次付费＜$70，距上次付费＞10日", "groupId": 9040, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "4500"}, {"groupName": "生命周期(20,∞)，$45≤单次付费＜$70，距上次付费＞10日", "groupId": 9040, "dataKey": "iap_single_max", "operator": "lt", "dataType": "number", "dataValue": "7000"}, {"groupName": "生命周期(20,∞)，$45≤单次付费＜$70，距上次付费＞10日", "groupId": 9040, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "11"}, {"groupName": "生命周期(20,∞)，单次付费≥$70，距上次付费＞12日", "groupId": 9050, "dataKey": "installedAt", "operator": "gt", "dataType": "days", "dataValue": "20"}, {"groupName": "生命周期(20,∞)，单次付费≥$70，距上次付费＞12日", "groupId": 9050, "dataKey": "iap_single_max", "operator": "gte", "dataType": "number", "dataValue": "7000"}, {"groupName": "生命周期(20,∞)，单次付费≥$70，距上次付费＞12日", "groupId": 9050, "dataKey": "last_pay_date", "operator": "gte", "dataType": "days", "dataValue": "13"}], "mapping": [{"id": 1, "userGroup": 0, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 2, "userGroup": 101, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 3, "userGroup": 102, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 4, "userGroup": 103, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 5, "userGroup": 1000, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 6, "userGroup": 1001, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 7, "userGroup": 1002, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 8, "userGroup": 1010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 9, "userGroup": 1011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 10, "userGroup": 1012, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 11, "userGroup": 1100, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 12, "userGroup": 1101, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 13, "userGroup": 1102, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 14, "userGroup": 1103, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 15, "userGroup": 1110, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 16, "userGroup": 1111, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 17, "userGroup": 1112, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 18, "userGroup": 1113, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 19, "userGroup": 1114, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 20, "userGroup": 1200, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 21, "userGroup": 1201, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 22, "userGroup": 1202, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 23, "userGroup": 1203, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 24, "userGroup": 1204, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 25, "userGroup": 1205, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 26, "userGroup": 1210, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 27, "userGroup": 1211, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 28, "userGroup": 1212, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 29, "userGroup": 1213, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 30, "userGroup": 1214, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 31, "userGroup": 1215, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 32, "userGroup": 1216, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 33, "userGroup": 6000, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 34, "userGroup": 6001, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 35, "userGroup": 6002, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 36, "userGroup": 6003, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 37, "userGroup": 6010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 38, "userGroup": 6011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 39, "userGroup": 6012, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 40, "userGroup": 6013, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 41, "userGroup": 6100, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 42, "userGroup": 6101, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 43, "userGroup": 6102, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 44, "userGroup": 6103, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 45, "userGroup": 6110, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 46, "userGroup": 6111, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 47, "userGroup": 6112, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 48, "userGroup": 6113, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 49, "userGroup": 6114, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 50, "userGroup": 6200, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 51, "userGroup": 6201, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 52, "userGroup": 6202, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 53, "userGroup": 6203, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 54, "userGroup": 6204, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 55, "userGroup": 6205, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 56, "userGroup": 6206, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 57, "userGroup": 6210, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 58, "userGroup": 6211, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 59, "userGroup": 6212, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 60, "userGroup": 6213, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 61, "userGroup": 6214, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 62, "userGroup": 6215, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 63, "userGroup": 6216, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 64, "userGroup": 7000, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 65, "userGroup": 7001, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 66, "userGroup": 7002, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 67, "userGroup": 7010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 68, "userGroup": 7011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 69, "userGroup": 7012, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 70, "userGroup": 7100, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 71, "userGroup": 7101, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 72, "userGroup": 7102, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 73, "userGroup": 7110, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 74, "userGroup": 7111, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 75, "userGroup": 7112, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 76, "userGroup": 7113, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 77, "userGroup": 7200, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 78, "userGroup": 7201, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 79, "userGroup": 7202, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 80, "userGroup": 7210, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 81, "userGroup": 7211, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 82, "userGroup": 7212, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 83, "userGroup": 7213, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 84, "userGroup": 2010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 85, "userGroup": 2011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 86, "userGroup": 2020, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 87, "userGroup": 2021, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 88, "userGroup": 2030, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 89, "userGroup": 2031, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 90, "userGroup": 2040, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 91, "userGroup": 2050, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 92, "userGroup": 2060, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 93, "userGroup": 3010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 94, "userGroup": 3011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 95, "userGroup": 3012, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 96, "userGroup": 3020, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 97, "userGroup": 3021, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 98, "userGroup": 3030, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 99, "userGroup": 3040, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 100, "userGroup": 3101, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 101, "userGroup": 3102, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 102, "userGroup": 3103, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 103, "userGroup": 3201, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 104, "userGroup": 3202, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 105, "userGroup": 3203, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 106, "userGroup": 3301, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 107, "userGroup": 3302, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 108, "userGroup": 3303, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 109, "userGroup": 3401, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 110, "userGroup": 3402, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 111, "userGroup": 3403, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 112, "userGroup": 3501, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 113, "userGroup": 3502, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 114, "userGroup": 4010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 115, "userGroup": 4011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 116, "userGroup": 4012, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 117, "userGroup": 4020, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 118, "userGroup": 4021, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 119, "userGroup": 4022, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 120, "userGroup": 4023, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 121, "userGroup": 4030, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 122, "userGroup": 4031, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 123, "userGroup": 4032, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 124, "userGroup": 4040, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 125, "userGroup": 4041, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 126, "userGroup": 4042, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 127, "userGroup": 4050, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 128, "userGroup": 4051, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 129, "userGroup": 4052, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 130, "userGroup": 4060, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 131, "userGroup": 4061, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 132, "userGroup": 4062, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 133, "userGroup": 4070, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 134, "userGroup": 4071, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 135, "userGroup": 5010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 136, "userGroup": 5011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 137, "userGroup": 5012, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 138, "userGroup": 5020, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 139, "userGroup": 5021, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 140, "userGroup": 5022, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 141, "userGroup": 5023, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 142, "userGroup": 5030, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 143, "userGroup": 5031, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 144, "userGroup": 5032, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 145, "userGroup": 5040, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 146, "userGroup": 5041, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 147, "userGroup": 5042, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 148, "userGroup": 5050, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 149, "userGroup": 5051, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 150, "userGroup": 5052, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 151, "userGroup": 5060, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 152, "userGroup": 5061, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 153, "userGroup": 5062, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 154, "userGroup": 5070, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 155, "userGroup": 5071, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 156, "userGroup": 9000, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 157, "userGroup": 9001, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 158, "userGroup": 9010, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 159, "userGroup": 9011, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 160, "userGroup": 9020, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 161, "userGroup": 9030, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 162, "userGroup": 9040, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}, {"id": 163, "userGroup": 9050, "adReward": 100, "adInterstitial": 200, "removeAdGroup": 100}], "adreward": [{"id": 1, "placeId": 1, "groupId": 100, "unlockLevel": 5, "showInterval": 3, "limitPerDay": 99}, {"id": 2, "placeId": 1, "groupId": 200, "unlockLevel": 5, "showInterval": 3, "limitPerDay": 99}, {"id": 3, "placeId": 1, "groupId": 300, "unlockLevel": 5, "showInterval": 3, "limitPerDay": 99}, {"id": 4, "placeId": 2, "groupId": 100, "unlockLevel": 5, "showInterval": 10, "limitPerDay": 99}, {"id": 5, "placeId": 2, "groupId": 200, "unlockLevel": 5, "showInterval": 10, "limitPerDay": 99}, {"id": 6, "placeId": 2, "groupId": 300, "unlockLevel": 5, "showInterval": 10, "limitPerDay": 99}, {"id": 7, "placeId": 3, "groupId": 100, "unlockScore": 500, "showInterval": 10, "limitPerDay": 99}, {"id": 8, "placeId": 3, "groupId": 200, "unlockScore": 500, "showInterval": 10, "limitPerDay": 99}, {"id": 9, "placeId": 3, "groupId": 300, "unlockScore": 500, "showInterval": 10, "limitPerDay": 99}, {"id": 10, "placeId": 4, "groupId": 100, "unlockLevel": 3, "showInterval": 30, "limitPerDay": 99}, {"id": 11, "placeId": 4, "groupId": 200, "unlockLevel": 3, "showInterval": 30, "limitPerDay": 99}, {"id": 12, "placeId": 4, "groupId": 300, "unlockLevel": 3, "showInterval": 30, "limitPerDay": 99}, {"id": 13, "placeId": 5, "groupId": 100, "unlockLevel": 3, "showInterval": 30, "limitPerDay": 99}, {"id": 14, "placeId": 5, "groupId": 200, "unlockLevel": 3, "showInterval": 30, "limitPerDay": 99}, {"id": 15, "placeId": 5, "groupId": 300, "unlockLevel": 3, "showInterval": 30, "limitPerDay": 99}], "adinterstitial": [{"id": 1, "placeId": 1, "groupId": 100, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 2, "placeId": 1, "groupId": 200, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 3, "placeId": 1, "groupId": 300, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 4, "placeId": 2, "groupId": 100, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 5, "placeId": 2, "groupId": 200, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 6, "placeId": 2, "groupId": 300, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 7, "placeId": 3, "groupId": 100, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 8, "placeId": 3, "groupId": 200, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 9, "placeId": 3, "groupId": 300, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 10, "placeId": 4, "groupId": 100, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 11, "placeId": 4, "groupId": 200, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 12, "placeId": 4, "groupId": 300, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 13, "placeId": 5, "groupId": 100, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 14, "placeId": 5, "groupId": 200, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}, {"id": 15, "placeId": 5, "groupId": 300, "unlockScore": 500, "unlockLevel": 6, "showInterval": 60, "limitPerDay": 999}], "adbanner": [{"id": 1, "placeId": 1, "groupId": 100, "unlockLevel": 3, "unlockScore": 500}, {"id": 2, "placeId": 1, "groupId": 200, "unlockLevel": 99999, "unlockScore": 99999}], "removead": [{"id": 1, "groupId": 100, "unlockLevel": 2, "unlockScore": 300, "limitPerDay": 1, "removeAdsGiftId": [3001, 3002]}, {"id": 2, "groupId": 200, "unlockLevel": 2, "unlockScore": 300, "limitPerDay": 1, "removeAdsGiftId": [3001, 3002]}]}