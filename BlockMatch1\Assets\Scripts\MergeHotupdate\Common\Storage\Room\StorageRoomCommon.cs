﻿using DragonPlus.Save;
using Newtonsoft.Json;
using UnityEngine;

namespace TMGame.Storage
{
    public class StorageRoomCommon:StorageBase
    {
        // 房间数据
        [JsonProperty]
        StorageDictionary<int,StorageRoom> roomData = new StorageDictionary<int,StorageRoom>(true);
        [JsonIgnore]
        public StorageDictionary<int,StorageRoom> RoomData
        {
            get
            {
                return roomData;
            }
        }
        
        
        // // 房间数据
        // [JsonProperty]
        // StorageDictionary<int,StorageRoomArea> areaData = new StorageDictionary<int,StorageRoomArea>(true);
        // [JsonIgnore]
        // public StorageDictionary<int,StorageRoomArea> AreaData
        // {
        //     get
        //     {
        //         return areaData;
        //     }
        // }
        
        // 房间顺序分组ID
        [JsonProperty]
        int collectRoomChapterId;
        [JsonIgnore]
        public int CollectRoomChapterId
        {
            get
            {
                return collectRoomChapterId;
            }
            set
            {
                if(collectRoomChapterId != value)
                {
                    collectRoomChapterId = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        
        // ---------------------------------//
        
        // 房间顺序分组ID
        [JsonProperty]
        int roomChapterId;
        [JsonIgnore]
        public int RoomChapterId
        {
            get
            {
                return roomChapterId;
            }
            set
            {
                if(roomChapterId != value)
                {
                    roomChapterId = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        
        // ---------------------------------//
        
        // 当前房间ID
        [JsonProperty]
        int curRoomId;
        [JsonIgnore]
        public int CurRoomId
        {
            get
            {
                return curRoomId;
            }
            set
            {
                if(curRoomId != value)
                {
                    curRoomId = value;
                    Profile.Instance.UpdateLocalVersion();
                    PlayerPrefs.SetInt("Player_CurRoomId", value);
                }
            }
        }
        
        
        // ---------------------------------//
        
        // 其他方式解锁的房间挂点
        [JsonProperty]
        StorageDictionary<int,int> unLockNodeByOther = new StorageDictionary<int,int>(true);
        [JsonIgnore]
        public StorageDictionary<int,int> UnLockNodeByOther
        {
            get
            {
                return unLockNodeByOther;
            }
        }
        
        
        [JsonProperty]
        StorageDictionary<int,int> unLockNodeByLevel = new StorageDictionary<int,int>(true);
        [JsonIgnore]
        public StorageDictionary<int,int> UnLockNodeByLevel
        {
            get
            {
                return unLockNodeByLevel;
            }
        }
        
        // 当前解锁的房间
        [JsonProperty]
        StorageDictionary<int,int> unLockRoom = new StorageDictionary<int,int>(true);
        [JsonIgnore]
        public StorageDictionary<int,int> UnLockRoom
        {
            get
            {
                return unLockRoom;
            }
        }
        // ---------------------------------//
        [JsonProperty]
        private bool isResetStorage = false;
        [JsonIgnore]
        public bool IsResetStorage
        {
            get
            {
                return isResetStorage;
            }
            set
            {
                if(isResetStorage != value)
                {
                    isResetStorage = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
    }
}