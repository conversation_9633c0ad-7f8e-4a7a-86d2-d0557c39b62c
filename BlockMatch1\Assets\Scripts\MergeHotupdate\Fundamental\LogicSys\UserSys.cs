// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/06/26/14:38
// Ver : 1.0.0
// Description : UserSys.cs
// ChangeLog :
// **********************************************

using System;
using System.Linq;
using DecorationRom;
using DragonPlus;
using DragonPlus.Account;
using DragonPlus.Config.Global;
using DragonPlus.Config.InGame;
using DragonPlus.Core;
using DragonPlus.Network;
using DragonPlus.Save;
using DragonPlus.Tracking;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using Newtonsoft.Json.Linq;
using TMGame.Storage;
using UnityEngine;

namespace TMGame
{
    public enum GlobalRewardType
    {
        FBBind = 1,
        FBLikeUs = 2,
        APPLEBind = 3,
        BeginReward = 4,
    }

    public class UserSys : LogicSys
    {
        public bool UserClickedLogin = false;

        public void LoginAsGuest()
        {
            if (SDK<IAccount>.Instance.HasLogin)
            {
                //登录状态ID为0，强制登录一次，避免状态出错
                if (SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId == 0)
                {
                    //   AccountManager.Instance.Clear();
                }
            }
            Debug.Log("登录LoginAsGuest");
            SDK<IAccount>.Instance.Login(OnLoginSuccess);
            UserClickedLogin = true;
        }


        public void OnBindAppleResult(bool success, string reason)
        {
            GameGlobal.GetMgr<UIMgr>().CloseWaiting();

            if (success)
            {
                OnLoginSuccess();

                if (!SDK<IStorage>.Instance.Get<StorageGlobal>().SocialBind.AppleBindRewardReceived)
                {
                    SDK<IStorage>.Instance.Get<StorageGlobal>().SocialBind.AppleFirstBindPendingReward = true;
                }
            }
            else
            {
                // GameBIManager.Instance.SendGameEvent(BiEventCooking.Types.GameEventType.GameEventBindFacebookFail,
                //     "UNKNOWN");
                UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
                {
                    content = LocalizationManager.Instance.GetLocalizedString("UI_bind_apple_fail_authen_error_text"),
                    showCloseBtn = false,
                    showMidBtn = true,
                };
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
            }
        }

        public async void OnLoginSuccess(bool success = true, string reason = "")
        {
            // if (success)
            //     SendLoginEvent();
            Debug.Log("进入登录回调Login");
            InitUserData();
            await SDK.Instance.OnUserLoginFinish();

            //GameGlobal.GetMgr<UIMgr>().CloseWaiting();

            // InitializeLocalUser();
            var playerId = SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId;
            
            RoomManager.Instance.Release();
            GameGlobal.GetMod<ModABTest>().OnLoginSuccess();
            GameGlobal.GetMod<RoomMod>().OnLoginSuccess();
            Debug.Log("登录成功,转到Home场景");
            GameGlobal.GetMod<ModFsm>().ChangeState<FsmState_Home>();
            Log.Info($"PlayerId:{playerId}");
        }

        public static void SendLoginEvent()
        {
            SDK<IAdjustAPI>.Instance.TrackEvent("social_login", "");
        }

        public void BindFacebook(Action cancelCallback)
        {
#if UNITY_EDITOR
            OnBindFbResult(true, "");
#else
            SDK<IAccount>.Instance.BindFacebook(OnBindFbResult, cancelCallback);
#endif
            UserClickedLogin = true;
        }

        public void OnBindFbResult(bool success, string reason)
        {
            GameGlobal.GetMgr<UIMgr>().CloseWaiting();
            if (success)
            {
                if (!SDK<IStorage>.Instance.Get<StorageGlobal>().SocialBind.FacebookBindRewardReceived)
                {
                    SDK<IStorage>.Instance.Get<StorageGlobal>().SocialBind.FacebookFirstBindPendingReward = true;
                    // 发奖逻辑暂时没有奖励
                }
            }
            else
            {
                BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventLoginFacebookFailed);
                UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
                {
                    content = LocalizationManager.Instance.GetLocalizedString("UI_bind_fb_fail_authen_error_text"),
                    showCloseBtn = false,
                    showMidBtn = true,
                };
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
            }

            EventBus.Dispatch(new EvtBindFacebook(success));
        }

        public void BindApple(Action cancelCallback)
        {
#if UNITY_EDITOR
            OnBindAppleResult(true, "");
#else
            SDK<IAccount>.Instance.BindApple(OnBindAppleResult, cancelCallback);
#endif
            UserClickedLogin = true;
        }

        public void InitializeLocalUser()
        {
            var isNewPlayer = SDK<IStorage>.Instance.RunOnce(() =>
            {
                if (!SDK<IStorage>.Instance.Get<StorageGlobal>().IsOldPlayer)
                {
                    SDK<IStorage>.Instance.Get<StorageGlobal>().IsOldPlayer = true;
                    SDK<IStorage>.Instance.Get<StorageGlobal>().FirstLaunchTime =
                        SDK<INetwork>.Instance.GetServerTime();
                }
            });
        }

        private void InitUserData()
        {
            // 新用户初始化
            var storageGlobal = SDK<IStorage>.Instance.Get<StorageGlobal>();
            var roomStorage = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
            if (!storageGlobal.IsOldPlayer)
            {
                storageGlobal.IsOldPlayer = true;
                storageGlobal.FirstLaunchTime = SDK<INetwork>.Instance.GetServerTime();
                storageGlobal.UserData.FirstLoginTime = SDK<INetwork>.Instance.GetServerTime();
                storageGlobal.UserData.FirstAppVersion = GameConfig.AppVersion;
                storageGlobal.UserData.FirstResVersion = GameConfig.CurResVersion;

                
                // 添加新用户初始道具
                int energyMaxCount = GameGlobal.GetMgr<ConfigMgr>()
                    .GetConstConfig<Table_Global_Global, int>("MaxUserEnergy");
                storageGlobal.Energy = energyMaxCount;
                // 初始金币
                int coinNum = GameGlobal.GetMgr<ConfigMgr>().GetConstConfig<Table_Global_Global, int>("InitCoin");
                GameGlobal.GetMod<ModBag>().AddItem(EItemType.Coin, coinNum,
                    new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.CreateProfile));
                // 初始装修币
                int decKeyNum = GameGlobal.GetMgr<ConfigMgr>().GetConstConfig<Table_Global_Global, int>("InitBuildCoin");
                GameGlobal.GetMod<ModBag>().AddItem(EItemType.Key, decKeyNum,
                    new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.CreateProfile));
                
                BIHelper.SendItemChangeEvent(BiEventBlockMatch1.Types.Item.Energy, energyMaxCount,
                    (ulong)energyMaxCount,
                    new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.CreateProfile));
                storageGlobal.LastAddEnergyTime = TMUtility.GetTimeStamp();
                // 新用户不重置装修存档
                roomStorage.IsResetStorage = true;
            }
            // 老玩家需要进行一次装修存档兼容操作
            if (!roomStorage.IsResetStorage)
            {
                roomStorage.IsResetStorage = true;
                RoomStorageExchange();
            }
            // 记录存档
            storageGlobal.LastLoginTime = SDK<INetwork>.Instance.GetServerTime();
            storageGlobal.UserData.LastLoginTime = SDK<INetwork>.Instance.GetServerTime();
            storageGlobal.UserData.LastAppVersion = GameConfig.AppVersion;
            storageGlobal.UserData.LastResVersion = GameConfig.CurResVersion;
            SDK<IStorage>.Instance.Get<StorageCommon>().ResVersion = GameConfig.CurResVersion;
            
            // 因为v2版本没存首次app版本和资源版本等信息，所以通过特殊判断加上
            if (storageGlobal.IsOldPlayer
                && string.IsNullOrEmpty(storageGlobal.UserData.FirstResVersion))
            {
                storageGlobal.UserData.FirstAppVersion = "v2";
                storageGlobal.UserData.FirstResVersion = "v2";
            }
        }

        // 因为装修资源全局修改，所以需要兼容线上玩家
        // 这是一个有风险的举动，需要仔细测试
        private void RoomStorageExchange()
        {
            var roomCommonStorage = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
            if (roomCommonStorage == null)
            {
                CLog.Error("RoomStorageExchange: roomCommonStorage is null");
                return;
            }
            // 1. 已经解锁的房间数量
            var oldUnlockRoomCnt = roomCommonStorage.UnLockRoom.Count;
            CLog.Info($"RoomStorageExchange: oldUnlockRoomCnt = {oldUnlockRoomCnt}");

            // 2. 获取当前room_roomchapter.json的配置，找到id为1005的数据块
            var roomChapters = TableConfigManage.Instance.GetRoomChapters();
            if (roomChapters == null || roomChapters.Count == 0)
            {
                CLog.Error("RoomStorageExchange: roomChapters is null or empty");
                return;
            }

            var targetChapter = roomChapters.FirstOrDefault(chapter => chapter.id == 1005);
            if (targetChapter == null || targetChapter.roomIds == null || targetChapter.roomIds.Length == 0)
            {
                CLog.Error("RoomStorageExchange: targetChapter with id 1005 not found or roomIds is empty");
                return;
            }

            CLog.Info($"RoomStorageExchange: found chapter 1005 with {targetChapter.roomIds.Length} rooms");

            // 3. 直接修改现有的StorageRoomCommon，将roomIds的前oldUnlockRoomCnt个房间设置为解锁状态

            // 第一步：清空所有相关存储数据
            roomCommonStorage.RoomData.Clear();
            roomCommonStorage.UnLockRoom.Clear();
            roomCommonStorage.UnLockNodeByOther.Clear();
            roomCommonStorage.UnLockNodeByLevel.Clear();
            CLog.Info("RoomStorageExchange: cleared all room related data");

            // 第二步：重建解锁房间和RoomData
            int roomsToUnlock = Math.Min(oldUnlockRoomCnt, targetChapter.roomIds.Length);
            for (int i = 0; i < roomsToUnlock; i++)
            {
                int roomId = targetChapter.roomIds[i];
                bool isLastRoom = (i == roomsToUnlock - 1);

                // 设置房间为解锁状态
                roomCommonStorage.UnLockRoom.Add(roomId, roomId);

                // 创建房间存储数据
                var storageRoom = new StorageRoom()
                {
                    Id = roomId,
                    IsFinish = !isLastRoom,    // 非最后房间为true
                    IsClean = !isLastRoom,     // 非最后房间为true
                    IsPlayAnim = !isLastRoom,  // 非最后房间为true
                    IsGetAward = !isLastRoom,  // 非最后房间为true
                    LastProgress = isLastRoom ? 0 : 1  // 最后房间为0，其他为1
                };

                // 处理roomNodes：最后房间保持为空，已完成房间设置所有节点为Received状态
                if (!isLastRoom)
                {
                    // 获取该房间的所有节点配置
                    var roomNodeConfigs = TableConfigManage.Instance.GetTableRoomNode(roomId);
                    if (roomNodeConfigs != null && roomNodeConfigs.Count > 0)
                    {
                        // 获取房间的装修配置
                        int roomResId = TableConfigManage.Instance.GetRoomResId(roomId);
                        var roomViewConfig = TableConfigManage.Instance.GetRoomNodeViewConfig(roomResId);

                        foreach (var nodeConfig in roomNodeConfigs)
                        {
                            // 从配置中获取该节点的第一个非旧装修ID
                            string defaultSelectId = GetFirstNewItemId(roomViewConfig, nodeConfig.id);

                            var storageNode = new StorageNode()
                            {
                                Id = nodeConfig.id,
                                Status = (int)RoomItem.Status.Received,  // 设置为已获得状态
                                SelectId = defaultSelectId  // 设置从配置读取的默认装修ID
                            };
                            storageRoom.RoomNodes.Add(nodeConfig.id, storageNode);
                        }
                        CLog.Info($"RoomStorageExchange: created {roomNodeConfigs.Count} nodes for completed room {roomId}");
                    }
                }
                // 最后房间的roomNodes保持为空，让系统正常初始化

                roomCommonStorage.RoomData.Add(roomId, storageRoom);
            }

            // 设置当前房间ID为最后一个解锁的房间
            if (roomsToUnlock > 0)
            {
                roomCommonStorage.CurRoomId = targetChapter.roomIds[roomsToUnlock - 1];
            }

            CLog.Info($"RoomStorageExchange: created {roomsToUnlock} room data entries");
        }

        /// <summary>
        /// 从房间配置中获取指定节点的第一个非旧装修ID
        /// </summary>
        private string GetFirstNewItemId(Home.Core.HomeViewConfig roomViewConfig, long nodeId)
        {
            if (roomViewConfig?.roomNodes == null)
                return "";

            // 通过TableConfigManage直接获取节点配置，不依赖Room对象
            var nodeConfig = TableConfigManage.Instance.GetRoomNode((int)nodeId);
            if (nodeConfig == null)
            {
                return "";
            }
            // 如果是清扫节点，返回空字符串
            if (nodeConfig.isClear)
            {
                return "";
            }

            // 查找对应的节点配置
            var nodeCfg = roomViewConfig.roomNodes.Find(x => x.id == nodeId);
            if (nodeCfg?.ItemCfgs == null || nodeCfg.ItemCfgs.Count == 0)
                return "";

            // 查找第一个非旧装修的ID
            foreach (var itemCfg in nodeCfg.ItemCfgs)
            {
                if (!itemCfg.oldFurItemCfg.isOld)
                {
                    return itemCfg.id;
                }
            }

            // 如果没有找到非旧装修，返回第一个装修ID
            return nodeCfg.ItemCfgs[0].id;
        }
    }
}