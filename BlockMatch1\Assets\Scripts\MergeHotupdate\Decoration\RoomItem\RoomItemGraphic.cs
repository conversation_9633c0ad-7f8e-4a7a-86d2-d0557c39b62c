﻿using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using DragonPlus.Core;
using Framework;
using Spine.Unity;
using TMGame;
using TMGame.Storage;
using DecorationRom.Core;


public class RoomItemGraphic
{
    private RoomItem _item;
    private SpriteRenderer[] _spriteRenderers;
    private Vector3 _screenPos;
    private Vector2 _offset;
    private bool _prefabLoaded;
    private PolygonCollider2D[] _colliders;
    private Animator _animator;
    private SkeletonAnimation[] _skeletonAnimations;
    private MeshRenderer _meshRenderer;
    
    private string[] animationNames = new[] { Room.ANI_FIRST_CONFIRM, Room.ANI_CLEAN, Room.ANI_CHOICE, Room.ANI_ONETIME, Room.ANI_NORMAL};
    private Dictionary<string, float> skeletonTime = new Dictionary<string, float>();
    private Dictionary<string, float> animatorTime = new Dictionary<string, float>();

    private List<GameObject> effects = new List<GameObject>();
    private bool isAutoPlayEffect = true;
    private CoroutineHandler _coroutine;
    private CoroutineHandler _stopEffectCoroutine;
    
    public Transform transform;
    
    private int initOrderLayer = 0;

    public Vector3 ScreenPos
    {
        get => _screenPos;
        set => _screenPos = value;
    }

    public Vector2 Offset
    {
        get => _offset;
    }

    public Vector3 WorldPosition;

    public RoomItemGraphic(RoomItem item, Vector2 offset)
    {
        _item = item;
        _offset = offset;

        SetItemSprite(offset);
    }

    public bool IsReady()
    {
        return _prefabLoaded;
    }

    public static Vector3 ConvertOffsetToScreenPos(Camera camera, Vector2 offset)
    {
        var dynamicOffset = offset - Room.CenterPosition;

        var scaleX = Screen.width / Room.DesignWidth;
        var scaleY = Screen.height / Room.DesignHeight;

        var maxScale = Mathf.Max(scaleX, scaleY);

        var x = Screen.width / 2f + dynamicOffset.x * maxScale;
        var y = Screen.height / 2f + dynamicOffset.y * maxScale;
        var z = 0f;
        if (!camera.orthographic)
        {
            z = 0f - camera.transform.position.z;
        }

        return new Vector3(x, y, z);
    }

    public void SetItemSprite(Vector2 offset)
    {
        if (_spriteRenderers != null)
        {
            setSpriteRenderer(offset);
            return;
        }

        var obj = GameGlobal.GetMgr<ResMgr>().GetGameObject(_item.Id).GetInstance();
       // var prefab = ResourcesManager.Instance.LoadResource<GameObject>(Path.Combine(string.Format(PathManager.ROOM_PREFAB_PATH, _item.room.ResId), _item.Id));
        if (obj == null)
        {
            CLog.Error($"{GetType()}.SetItemSprite, cannot load item, room = {_item.room.ResId}  abroom = {_item.room.ResId}, item = {_item.Id}");
            return;
        }

        _prefabLoaded = true;
       // var obj = GameObject.Instantiate(prefab);
        transform = obj.transform;
#if UNITY_EDITOR
        obj.name = _item.Id;
#endif
        _spriteRenderers = obj.GetComponentsInChildren<SpriteRenderer>();
            
        // if (!string.IsNullOrEmpty(_item.Controller))
        // {
        //     _animator = obj.GetComponent<Animator>();
        //     if(_animator == null) 
        //         _animator = obj.AddComponent<Animator>();
        //
        //     string resName = _item.Controller.Replace("/", "_");//Path.GetFileName(_item.Controller);
        //     _animator.runtimeAnimatorController = GameGlobal.GetMgr<ResMgr>().GetRes<RuntimeAnimatorController>(resName).GetInstance(obj);
        //     // _animator.runtimeAnimatorController = 
        //     //     ResourcesManager.Instance.LoadResource<RuntimeAnimatorController>(
        //     //         Path.Combine(PathManager.ROOM_ANIM_PATH, _item.Controller), false,true,_item.Controller);
        // }

        Transform oneChild = obj.transform.Find("01");
        if (null != oneChild)
        {
            foreach (var effect in _item.Effects)
            {
                GameObject effectObj =  GameGlobal.GetMgr<ResMgr>().GetGameObject(effect.prefabName).GetInstance();
                // GameObject effectPrefab = ResourcesManager.Instance.LoadResource<GameObject>(Path.Combine(PathManager.ROOM_EFFECT_PATH, effect.prefabName));
                if (null == effectObj)
                    continue;
                
               // GameObject effectObj = GameObject.Instantiate(effectPrefab);
                effectObj.name = effect.objectName;
                effectObj.transform.SetParent(oneChild);
                effectObj.transform.Reset();
                effect.Cover(effectObj.transform);
                
                effects.Add(effectObj);
            }

            StopEffect();
        }
        _colliders = obj.GetComponentsInChildren<PolygonCollider2D>();

        //先获取自定义动画
        _animator = obj.GetComponent<Animator>();
        if (!_animator)
            _animator = null;
        
        _skeletonAnimations = obj.GetComponentsInChildren<SkeletonAnimation>();
        if (_skeletonAnimations != null && _skeletonAnimations.Length > 0)
        {
            _meshRenderer = _skeletonAnimations[0].transform.GetComponent<MeshRenderer>();
        }
        
        TMUtility.EditorChangeSpineShader(obj);
        
        obj.transform.SetParent(_item.Node.transform);
        obj.transform.Reset();
        obj.layer = LayerMask.NameToLayer(RoomResLayer.Item.ToString());

        setSpriteRenderer(offset);
        InitTimes();
        
        OnNormal();
    }

    private void setSpriteRenderer(Vector2 offset)
    {
        transform.gameObject.SetActive(true);
        _screenPos = ConvertOffsetToScreenPos(_item.room.Camera, offset);
        WorldPosition = _item.room.Camera.ScreenToWorldPoint(_screenPos);
    }

    public void OnNormal()
    {
        PlayAnimation(Room.ANI_NORMAL);
    }
    
    public void OnTap()
    {
        PlayAnimation(Room.ANI_TAP);
    }

    public void OnSelected()
    {
        PlayAnimation(Room.ANI_CHOICE);
    }

    public void OnChangeDifferentItem(Action animEnd = null)
    {
        PlayAnimation(Room.ANI_FIRST_CONFIRM,animEnd);
    }

    public float GetChangeDifferentItemLength()
    {
        return AnimatorUtils.GetClipInfo(_animator, Room.ANI_FIRST_CONFIRM)?.length ?? 0f;
    }

    public void OnConfirm(Action animEnd = null)
    {
        PlayAnimation(Room.ANI_FIRST_CONFIRM, animEnd);
    }

    public void OnDeSelect()
    {
        PlayAnimation(Room.ANI_NORMAL);
    }

    public void Clear()
    {
        transform.gameObject.SetActive(false);
    }

    public void PlayCleanRoomAnim(Action animEnd = null)
    {
        transform.gameObject.SetActive(true);
        PlayAnimation(Room.ANI_CLEAN, animEnd);
    }

    public void PlayAnim()
    {
        transform.gameObject.SetActive(true);
        PlayAnimation(Room.ANI_FIRST_CONFIRM);
    }
    public void SetLastRender(List<Sprite> lastSpriteList)
    {
        var childCount = transform.childCount;
        for (int i = 0; i < childCount; i++)
        {
            var render = transform.GetChild(i);
            var last = render.Find("last");
            if (last == null)
                continue;
            
            if (i < lastSpriteList.Count)
            {
                last.GetComponent<SpriteRenderer>().sprite = lastSpriteList[i];
            }
        }
    }

    private void PlayAnimation(string name, Action animEnd = null)
    {
        string animName = SwitchAnimName(name);
        OneTimeChoice(name);
        
        float time = GetAnimationTime(animName);

        SpecialLogic(name, false); 
        if (time > 0)
        {
            if(_coroutine != null)
                
               GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_coroutine);

            if (_stopEffectCoroutine != null)
            {
               GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_stopEffectCoroutine);
                _stopEffectCoroutine = null;
            }
                
            _coroutine =GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(time, () =>
            {
                if(isAutoPlayEffect)
                    PlayEffect();

                SpecialLogic(name, true);
                _item.Node.SetShowNodes(true);
                
                isAutoPlayEffect = true;

                if(effects != null && effects.Count > 0)
                {
                    _stopEffectCoroutine =GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1.5f, () =>
                    {
                        PlayEffect(false);
                        
                        if (animEnd != null)
                            animEnd();

                        _stopEffectCoroutine = null;
                    }));
                }
                else
                {
                    SpecialLogic(name, true);
                    _item.Node.SetShowNodes(true);
                    if (animEnd != null)
                        animEnd();
                }
            }));
        }
        else
        {
            SpecialLogic(name, true);
          
            bool isShowNode = _item.room.Data.GetNodeStatus((int)_item.Node.Id) == RoomItem.Status.Received;
            _item.Node.SetShowNodes(isShowNode);
            
            if (animEnd != null)
                animEnd();
        }
        
        if (_animator != null)
        {
            if (animatorTime.ContainsKey(animName))
            {
                _animator.Play(animName);

                if (skeletonTime.ContainsKey(animName))
                {
                    StorageNode storageNode = _item.room.Data.GetNodeStorage(_item.Node.Id);
                    if (storageNode != null)
                    {
                        if (storageNode.Status == (int)RoomItem.Status.Received && animName == Room.ANI_NORMAL)
                            PlaySkeletonAnim(animName);
                    }
                    
                    return;
                }
            }
        }
        
      
        PlaySkeletonAnim(animName);
    }

    private void PlaySkeletonAnim(string animName)
    {
        if(_skeletonAnimations == null)
            return;
        
        foreach (var p in _skeletonAnimations)
        {
            if (p == null || p.skeletonDataAsset == null) continue;
            var theSkeletonData = p.skeletonDataAsset.GetSkeletonData(false);
            if (theSkeletonData == null) continue;
            var animationObject = theSkeletonData.FindAnimation(animName);
            if(animationObject == null)
                continue;
            
            p.ClearState();
            if (p.skeleton != null)
            {
                p.skeleton.SetSlotsToSetupPose();
            }
            p.AnimationName = null;
            p.AnimationName = animName;
            p.Update(0);
            p.state.SetAnimation(0, animName, false);
        }
    }
    
    private void InitTimes()
    {
        skeletonTime.Clear();
        animatorTime.Clear();

        if (_animator != null&&_animator.runtimeAnimatorController!=null)
        {
            foreach (var kv in _animator.runtimeAnimatorController.animationClips)
            {
                foreach (var name in animationNames)
                {
                   if(kv.name != name)
                       continue;

                   if (!animatorTime.ContainsKey(name))
                   {
                       animatorTime.Add(name, kv.length);
                       continue;
                   }

                   animatorTime[name] = Mathf.Max(animatorTime[name], kv.length);
                }
            }
        }

        if (_skeletonAnimations != null)
        {
            foreach (var kv in _skeletonAnimations)
            {
                foreach (var name in animationNames)
                {
                    Spine.Animation anim = kv.skeleton.Data.FindAnimation(name);
                    if(anim == null)
                        continue;
                    
                    if (!skeletonTime.ContainsKey(name))
                    {
                        skeletonTime.Add(name, anim.Duration);
                        continue;
                    }

                    skeletonTime[name] = Mathf.Max(skeletonTime[name], anim.Duration);
                }
            }
        }
    }

    public float GetAnimationTime(string name)
    {
        float time = 0f;
        if (skeletonTime.ContainsKey(name))
            time = skeletonTime[name];

        if (animatorTime.ContainsKey(name))
            time = animatorTime[name] > time ? animatorTime[name] : time;

        if (time <= 0 && name == Room.ANI_FIRST_CONFIRM)
        {
            time = 2;
        }
        return time;
    }

    public void PlayEffect(bool isShow = true)
    {
        if(effects == null || effects.Count == 0)
            return;
 
        foreach (var kv in effects)
        {
            if (kv == null) continue;
            if (kv.gameObject == null) continue;
             kv.gameObject.SetActive(false);
             if(isShow)
                 kv.gameObject.SetActive(true);   
        }
    }
    
    public void StopEffect()
    {
        if(effects == null || effects.Count == 0)
            return;

        foreach (var kv in effects)
        {
            if (kv == null) continue;
            if (kv.gameObject == null) continue;
            kv.gameObject.SetActive(false);
        }
    }
    

    public void SetPlayEffect(bool playEffect)
    {
        isAutoPlayEffect = playEffect;
    }
    
    public bool TouchMe(Vector2 screenPos)
    {
        var worldPos = _item.room.Camera.ScreenToWorldPoint(screenPos);
        foreach (var collider in _colliders)
        {
            if (collider.OverlapPoint(worldPos)) return true;
        }

        return false;
    }

    private void SpecialLogic(string name, bool recover = false)
    {
        if (name != Room.ANI_FIRST_CONFIRM)
            return;

        if (!recover)
            _item.Node.HideNodes();
        else
        {
            _item.Node.RecoverNodes();
            _item.Node.SetShowNodes(true);
        }

        if (_item.Node.Config.orderLayer <= 0)
            return;
        
        if (_meshRenderer == null)
            return;
    
        if(!recover)
            initOrderLayer = _meshRenderer.sortingOrder;

        _meshRenderer.sortingOrder = recover ? initOrderLayer : _item.Node.Config.orderLayer;
    }

    private string SwitchAnimName(string name)
    {
        if (name != Room.ANI_FIRST_CONFIRM)
            return name;
        
        if (_item.Node == null || _item.Node.Config == null || !_item.Node.Config.oneTimeClean)
            return name;

        if (CanOneTimeChoice())
            return Room.ANI_ONETIME;
        
        return name;
    }
    public bool CanOneTimeChoice()
    {
        if (_item.room == null || _item.room.Data == null)
            return false;

        StorageNode storageNode = _item.room.Data.GetNodeStorage(_item.Node.Id);
        if (storageNode == null)
            return false;

        return !storageNode.OneTimeChoice;
    }

    private void OneTimeChoice(string name)
    {
        if(name != Room.ANI_FIRST_CONFIRM)
            return;
        
        if (_item.room == null || _item.room.Data == null)
            return;

        StorageNode storageNode = _item.room.Data.GetNodeStorage(_item.Node.Id);
        if (storageNode == null)
            return;

        if(storageNode.OneTimeChoice)
            return;
        
        storageNode.OneTimeChoice = true;
    }
}