﻿using System.Collections;
using DragonPlus.Core;
using Framework;
using TMGame;
using UnityEngine;
using RoomId = System.Int32;
namespace DecorationRom.Core
{
    public class RoomGraphic
    {
        public Transform transform;

        private Room _room;
        private SpriteRenderer[] _bgSprites;

        public RoomGraphic(Room room)
        {
            _room = room;

            InitRoomGraphic();
            InitRoomResGraphic();
        }

        public void Dispose()
        {
            if (transform != null)
                Object.Destroy(transform.gameObject);
        }

        public bool IsReady()
        {
            return _bgSprites != null;
        }

        private void InitRoomGraphic()
        {
            var obj = new GameObject($"Room_{_room.Id}");
            transform = obj.transform;
            transform.Reset();
        }

        private void InitRoomResGraphic()
        {
            // var path = $"room2d_Room{_room.ResId}// Path.Combine(string.Format(PathManager.ROOM_PREFAB_PATH, _room.ResId), "room2d_Room{_room.ResId}");

            string path = $"room2d_Room{_room.ResId}";
            var prefab = GameGlobal.GetMgr<ResMgr>().GetGameObject(path).GetInstance();
            if (prefab == null)
            {
                CLog.Error($"{GetType()}.RoomGraphic, load resource error, path = {path}");
                return;
            }

#if UNITY_EDITOR
            prefab.name = "Background";
#endif
            prefab.transform.SetParent(transform);
            prefab.layer = LayerMask.NameToLayer(RoomResLayer.Room.ToString());
            _bgSprites = prefab.GetComponentsInChildren<SpriteRenderer>();
        }

        public void Show()
        {
            if (transform == null)
                return;

            transform.gameObject.transform.localPosition = Vector3.zero;
        }

        private Vector3 hidePosition = new Vector3(10000, 10000, 0);
        public void Hide()
        {
            if (transform == null)
                return;

            transform.gameObject.transform.localPosition = hidePosition;
        }
    }
}