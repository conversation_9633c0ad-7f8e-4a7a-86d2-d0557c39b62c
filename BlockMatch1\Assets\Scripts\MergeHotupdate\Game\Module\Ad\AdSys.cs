using System;
using System.Collections.Generic;
using DragonPlus.Ad;
using DragonPlus.Ad.Max;
using DragonPlus.Config;
using DragonPlus.ConfigHub.Ad;
using DragonPlus.Core;
using DragonPlus.InAppPurchasing;
using DragonPlus.Network;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using TMGame.Storage;
using UnityEngine;

namespace TMGame
{
    public enum eAdInterstitial
    {
        None = 0, //
        ReplayGameBySetting = 1, //从设置界面重开游戏 
        QuitGameByLevelFail = 2, //关卡失败退出 
        QuitGameBySetting = 3, //从设置界面退出
        QuitGameByLevelWin = 4, //关卡胜利退出 
        QuitGameByEndlessGameOver = 5, //无尽模式结算界面退出 
    }

    public enum eAdReward
    {
        BuyEnergy = 1, //购买体力
        GameLevelRevive = 2, // 关卡复活
        GameRevive = 3, //复活
        GameWinDoubleReward = 4, //关卡胜利界面装修币翻倍
        DecorationUnlock = 5,//广告解锁装修
    }

    public enum eAdBanner
    {
        None = 0,
        FindTM = 1, //局内
    }

    public class AdModel
    {
        private StorageAd _storageBase;

        public AdModel()
        {
            _storageBase = SDK<IStorage>.Instance.Get<StorageAd>();
        }

        public bool CheckUnlockRewardContainsKey(int key)
        {
            return _storageBase.UnlockRewardState.ContainsKey(key);
        }

        public bool GetUnlockRewardStateKeyValue(int key)
        {
            return _storageBase.UnlockRewardState[key];
        }

        public void SetUnlockRewardStateKeyValue(int key, bool value)
        {
            _storageBase.UnlockRewardState[key] = value;
        }

        public void AddKeyRewardState(int key)
        {
            _storageBase.UnlockRewardState.Add(key, false);
        }

        public long GetResetStateTime()
        {
            return _storageBase.ResetStateTime;
        }

        public void ResetState()
        {
            _storageBase.ResetStateTime = SDKUtil.TimeDate.GetTomorrowTimestamp();
            _storageBase.RewardWatchLastTimeStamp.Clear();
            _storageBase.RewardWatchCount.Clear();
            _storageBase.InterWatchLastTimeStamp.Clear();
            _storageBase.InterWatchCount.Clear();
            Debug.Log($"[AD] ResetState.");
        }

        public long GetRewardWatchLastTimeStamp(int key)
        {
            long lastTimeStamp;
            _storageBase.RewardWatchLastTimeStamp.TryGetValue(key, out lastTimeStamp);
            return lastTimeStamp;
        }

        public int GetRewardWatchCount(int key)
        {
            int count;
            _storageBase.RewardWatchCount.TryGetValue(key, out count);
            return count;
        }

        public bool CheckRewardWatchCountContainsKey(int key)
        {
            return _storageBase.RewardWatchCount.ContainsKey(key);
        }

        public void AddRewardWatchCountKey(int key)
        {
            _storageBase.RewardWatchCount.Add(key, 0);
        }

        public void UpdateRewardWatchCountKeyValue(int key, int count)
        {
            _storageBase.RewardWatchCount[key] = _storageBase.RewardWatchCount[key] + count;
        }

        public bool CheckRewardWatchLastTimeStampContainsKey(int key)
        {
            return _storageBase.RewardWatchLastTimeStamp.ContainsKey(key);
        }

        public void AddRewardWatchLastTimeStampKey(int key, long nowStamp)
        {
            _storageBase.RewardWatchLastTimeStamp.Add(key, nowStamp);
        }

        public void UpdateRewardWatchLastTimeStampValue(int key, long nowStamp)
        {
            _storageBase.RewardWatchLastTimeStamp[key] = nowStamp;
        }

        public bool CheckUnlockInterContainsKey(int key)
        {
            return _storageBase.UnlockInterState.ContainsKey(key);
        }

        public bool GetUnlockInterStateKeyValue(int key)
        {
            return _storageBase.UnlockInterState[key];
        }

        public void SetUnlockInterStateKeyValue(int key, bool value)
        {
            _storageBase.UnlockInterState[key] = value;
        }

        public void AddKeyInterState(int key)
        {
            _storageBase.UnlockInterState.Add(key, false);
        }

        public bool CheckInterWatchCount(int key)
        {
            return _storageBase.InterWatchCount.ContainsKey(key);
        }

        public void AddInterWatchCountKey(int key)
        {
            _storageBase.InterWatchCount.Add(key, 0);
        }

        public void AddInterWatchCountValue(int key, int value)
        {
            _storageBase.InterWatchCount[key] = _storageBase.InterWatchCount[key] + value;
        }

        public int GetInterWatchCountValue(int key)
        {
            int count;
            _storageBase.InterWatchCount.TryGetValue(key, out count);
            return count;
        }

        public bool CheckInterWatchLastTimeStampContainsKey(int key)
        {
            return _storageBase.InterWatchLastTimeStamp.ContainsKey(key);
        }

        public void AddInterWatchLastTimeStampKey(int key, long nowStamp)
        {
            _storageBase.InterWatchLastTimeStamp.Add(key, nowStamp);
        }

        public void UpdateInterWatchLastTimeStampValue(int key, long nowStamp)
        {
            _storageBase.InterWatchLastTimeStamp[key] = nowStamp;
        }

        public long GetInterWatchLastTimeStamp(int key)
        {
            long lastTimeStamp;
            _storageBase.InterWatchLastTimeStamp.TryGetValue(key, out lastTimeStamp);
            return lastTimeStamp;
        }

        public void RecordInterWatchLastTimeStamp(eAdInterstitial pos)
        {
            int key = (int)pos;
            var nowStamp = SDKUtil.TimeDate.CurrentTimeInSecond();
            if (!_storageBase.InterWatchLastTimeStamp.ContainsKey(key)) _storageBase.InterWatchLastTimeStamp.Add(key, nowStamp);
            _storageBase.InterWatchLastTimeStamp[key] = nowStamp;
        }

        public StorageAd GetStorage()
        {
            return _storageBase;
        }
    }

    public class AdSys : LogicSys
    {
        /// <summary>
        /// GM工具控制广告跳过功能的开关，默认为false
        /// </summary>
        public static bool isOpenVideoAds = false;
        
        private Dictionary<int, AdReward> AdRewardConfigs = new Dictionary<int, AdReward>();
        private Dictionary<int, AdInterstitial> AdInterstitialConfigs = new Dictionary<int, AdInterstitial>();
        private Dictionary<int, AdBanner> AdBannerConfigs = new Dictionary<int, AdBanner>();

        private AdModel _model;

        private bool _isInit;

        private Action<AdPlayResult, string> _rewardCallBack = null;

        private StorageRemoveAd _storageRemoveAd;
        private bool _showingBanner;
        public bool PlayedInterstitalDurLogin = false;

        private bool GameWinDoubleRewardShow = false;
        public bool ShowingBanner
        {
            get
            {
                return _showingBanner;
            }
            set
            {
                _showingBanner = value;
            }
        }

        public override void OnShutDown()
        {
            // ///////////////激励视频////////////////////
            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNewuserlevel,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason
                    .CommonMonetizationEventReasonAdSeperateCooldown,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdOverdisplay,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNofill,
                null);

            ////////插屏///////
            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdMysteryPower,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNewuserlevel,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdPaid,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdCommonCooldown,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason
                    .CommonMonetizationEventReasonAdSeperateCooldown,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdOverdisplay,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNofill,
                null);

            ///////////////Banner////////////////////
            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Banner,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNofill,
                null);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Banner,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdPaid,
                null);
            base.OnShutDown();
        }

        public override void Start()
        {
            base.Start();

            _model = new AdModel();

            if (_isInit)
            {
                Debug.LogError($"[AD] AdLogicManager already initialed.");
                return;
            }

            _isInit = true;

            _storageRemoveAd = SDK<IStorage>.Instance.Get<StorageGlobal>().RemoveAd;

            /////////////激励视频////////////////////
            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNewuserlevel,
                pos => !IsRewardUnlock((eAdReward)Enum.Parse(typeof(eAdReward), pos)));

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason
                    .CommonMonetizationEventReasonAdSeperateCooldown,
                pos => IsRewardInCD((eAdReward)Enum.Parse(typeof(eAdReward), pos)));

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdOverdisplay,
                pos => IsRewardCountReachUpperLimit((eAdReward)Enum.Parse(typeof(eAdReward), pos)));

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.RewardVideo,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNofill,
                pos => !SDK<IAdProvider>.Instance.IsRewardVideoReady() && !GameUtils.IsInEditorEnv());

            ////////插屏///////
            // AdLogicManager.Instance.RegisterFailDelegate(Dlugin.AD_Type.Interstitial,
            //     BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdMysteryPower,
            //     pos => AdLogicManager.Instance.specialOrder);

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNewuserlevel,
                pos => !IsInterUnlock((eAdInterstitial)Enum.Parse(typeof(eAdInterstitial), pos)));

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdPaid,
                pos => IsRemoveAd());

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason
                    .CommonMonetizationEventReasonAdSeperateCooldown,
                pos => IsInterInCD((eAdInterstitial)Enum.Parse(typeof(eAdInterstitial), pos)));

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdOverdisplay,
                pos => IsInterCountReachUpperLimit((eAdInterstitial)Enum.Parse(typeof(eAdInterstitial), pos)));

            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Interstitial,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNofill,
                pos => !SDK<IAdProvider>.Instance.IsInterstitialReady() && !GameUtils.IsInEditorEnv());

            ///////////////Banner////////////////////
            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Banner,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdNofill,
                pos => !IsBannerUnlock((eAdBanner)Enum.Parse(typeof(eAdBanner), pos)));
            SDK<IAdProvider>.Instance.RegisterFailDelegate(AD_Type.Banner,
                BiEventCommon.Types.CommonMonetizationAdEventFailedReason.CommonMonetizationEventReasonAdPaid,
                pos => IsRemoveAd());

            ResetConfig();

            InitPreBannerVerPlayer();
        }

        public override void SubscribeEvents()
        {
            base.SubscribeEvents();
            SubscribeEvent<EventConfigHubUpdatedEvent>(OnEventConfigHubUpdated);
        }

        private void OnEventConfigHubUpdated(EventConfigHubUpdatedEvent evt)
        {
            ResetConfig();
        }

        private void InitPreBannerVerPlayer()
        {
            if (_storageRemoveAd != null && !_storageRemoveAd.InitializedPreBannerVerPlayer)
            {
                if (!_storageRemoveAd.RemoveBannerAd && TMGame.GameGlobal.GetMod<IAPSys>().IsPlayerPurchased())
                {
                    _storageRemoveAd.RemoveBannerAd = true;
                }
                _storageRemoveAd.InitializedPreBannerVerPlayer = true;
            }
        }

        /// <summary>
        /// 重置配置
        /// </summary>
        private void ResetConfig()
        {
            Debug.Log("Fetch Remote Config, ResetConfig");
            AdRewardConfigs.Clear();
            var adRewardAll = AdConfigManager.Instance.GetConfig<AdReward>();
            var cfgAdReward = adRewardAll.FindAll(c => c.GroupId == GetAdRewardCurrentGroup());
            foreach (var cfg in cfgAdReward)
            {
                AdRewardConfigs[cfg.PlaceId] = cfg;
            }

            AdInterstitialConfigs.Clear();
            var adInterstitialAll = AdConfigManager.Instance.GetConfig<AdInterstitial>();
            var cfgAdInterstitial = adInterstitialAll.FindAll(c => c.GroupId == GetAdInterstitialCurrentGroup());
            foreach (var cfg in cfgAdInterstitial)
            {
                AdInterstitialConfigs[cfg.PlaceId] = cfg;
            }

            AdBannerConfigs.Clear();
            // var adBannerAll = AdConfigManager.Instance.GetConfig<AdBanner>();
            // var cfgAdBanner = adBannerAll.FindAll(c => c.GroupId == GetAdBannerCurrentGroup());
            // foreach (var cfg in cfgAdBanner)
            // {
            //     AdBannerConfigs[cfg.PlaceId] = cfg;
            // }
        }

        public int GetCurGroup()
        {
            var groups = AdConfigManager.Instance.GetConfig<Mapping>();
            if (groups != null && groups.Count > 0)
            {
                var group = groups[0].UserGroup;
                return group;
            }
            return 0;
        }

        /// <summary>
        /// 获取 AdReward 当前用户分层
        /// </summary>
        /// <returns></returns>
        public int GetAdRewardCurrentGroup()
        {
            var groups = AdConfigManager.Instance.GetConfig<Mapping>();
            if (groups != null && groups.Count > 0)
            {
                //Debug.Log($"RewardGroup:{groups[0].AdReward}");
                Log.Debug($"GetAdRewardCurrentGroup: {groups[0].AdReward}");
                Debug.Log($"GetAdRewardCurrentGroup: {groups[0].AdReward}");
                return groups[0].AdReward;
            }
            //Debug.Log($"RewardGroup:Default 100");
            Log.Debug($"GetAdRewardCurrentGroup: 300 默认,没有获得mapping值");
            Debug.Log($"GetAdRewardCurrentGroup: 300 默认,mapping.count = {groups?.Count}");
            return 300;
        }

        /// <summary>
        /// 获取 AdInterstitial 当前用户分层
        /// </summary>
        /// <returns></returns>
        public int GetAdInterstitialCurrentGroup()
        {
            var groups = AdConfigManager.Instance.GetConfig<Mapping>();
            if (groups != null && groups.Count > 0) return groups[0].AdInterstitial;
            return 200;
        }

        /// <summary>
        /// 获取 Banner 当前用户分层
        /// </summary>
        /// <returns></returns>
        public int GetBannerCurrentGroup()
        {
            var groups = AdConfigManager.Instance.GetConfig<Mapping>();
           // if (groups != null && groups.Count > 0) return groups[0].AdBanner;
            return 100;
        }

        /// <summary>
        /// 激励视频是否解锁
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        public bool IsRewardUnlock(eAdReward pos)
        {
            int key = (int)pos;
            if (!_model.CheckUnlockRewardContainsKey(key))
            {
                _model.AddKeyRewardState(key);
            }

            if (!AdRewardConfigs.ContainsKey(key))
            {
                return false;
            }

            var conf = AdRewardConfigs[key];
            if ( StorageExtension.GameBlockLevelStorage.CurLevelInfo.levelId + 1 >= conf.UnlockLevel
                 && StorageExtension.GameEndlssStorage.CurFirstScore >= conf.UnlockScore
                 )
            {
                _model.SetUnlockRewardStateKeyValue(key, true);
            }
            return _model.GetUnlockRewardStateKeyValue(key);
        }

        /// <summary>
        /// 激励视频是否在CD中
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        private bool IsRewardInCD(eAdReward pos)

        {
            TryToResetState();
            int key = (int)pos;
            long lastTimeStamp = _model.GetRewardWatchLastTimeStamp(key);
            AdReward cfg;
            AdRewardConfigs.TryGetValue(key, out cfg);
            long tempValue = cfg != null ? cfg.ShowInterval : 0;
            return SDKUtil.TimeDate.CurrentTimeInSecond() - lastTimeStamp < tempValue;
        }

        /// <summary>
        /// 尝试重置状态
        /// </summary>
        private void TryToResetState()
        {
            var currentTime = SDKUtil.TimeDate.CurrentTimeInSecond();
            if (currentTime > _model.GetResetStateTime())
            {
                _model.ResetState();
                ResetConfig();
            }
        }

        /// <summary>
        /// 激励视频次数是否已达上限
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        private bool IsRewardCountReachUpperLimit(eAdReward pos)
        {
            TryToResetState();
            int key = (int)pos;
            int count = _model.GetRewardWatchCount(key);
            AdReward cfg;
            AdRewardConfigs.TryGetValue(key, out cfg);
            long tempValue = cfg != null ? cfg.LimitPerDay : 0;
            if (cfg != null)
            {
                Debug.Log($"RewardCountLimit:Count:{tempValue},cfgId:{cfg.Id}");
            }
            else
            {
                Debug.Log($"RewardCountLimit:{tempValue}");
            }
            return count >= tempValue;
        }

        /// <summary>
        /// 激励视频播放后回调
        /// </summary>
        /// <param name="pos"></param>
        private void OnRewardPlay(eAdReward pos)
        {
            int key = (int)pos;
            if (!_model.CheckRewardWatchCountContainsKey(key))
            {
                _model.AddRewardWatchCountKey(key);
            }

            _model.UpdateRewardWatchCountKeyValue(key, 1);

            var nowStamp = SDKUtil.TimeDate.CurrentTimeInSecond();
            if (!_model.CheckRewardWatchLastTimeStampContainsKey(key))
                _model.AddRewardWatchLastTimeStampKey(key, nowStamp);
            _model.UpdateRewardWatchLastTimeStampValue(key, nowStamp);
        }

        /// <summary>
        /// 插频是否解锁
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        public bool IsInterUnlock(eAdInterstitial pos)
        {
            int key = (int)pos;

            if (!_model.CheckUnlockInterContainsKey(key))
            {
                _model.AddKeyInterState(key);
            }

            if (!AdInterstitialConfigs.ContainsKey(key))
            {
                return false;
            }
     
            if (!_model.GetUnlockInterStateKeyValue(key))
                _model.SetUnlockInterStateKeyValue(key, true);
            return _model.GetUnlockInterStateKeyValue(key);
        }

        /// <summary>
        /// 插屏播放前回调
        /// </summary>
        /// <param name="pos"></param>
        private void OnInterPlay(eAdInterstitial pos)
        {
            int key = (int)pos;
            if (!_model.CheckInterWatchCount(key))
            {
                _model.AddInterWatchCountKey(key);
            }

            _model.AddInterWatchCountValue(key, 1);

            var nowStamp = SDKUtil.TimeDate.CurrentTimeInSecond();
            if (!_model.CheckInterWatchLastTimeStampContainsKey(key))
                _model.AddInterWatchLastTimeStampKey(key, nowStamp);
            _model.UpdateInterWatchLastTimeStampValue(key, nowStamp);
        }

        public void RecordInterWatchLastTimeStamp(eAdInterstitial pos)
        {
            _model.RecordInterWatchLastTimeStamp(pos);
        }

        /// <summary>
        /// 插屏是否在CD中
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        private bool IsInterInCD(eAdInterstitial pos)
        {
            TryToResetState();
            int key = (int)pos;
            long lastTimeStamp = _model.GetInterWatchLastTimeStamp(key);
            AdInterstitial cfg;
            AdInterstitialConfigs.TryGetValue(key, out cfg);
            long tempValue = cfg != null ? cfg.ShowInterval : 0;
            return SDKUtil.TimeDate.CurrentTimeInSecond() - lastTimeStamp < tempValue;
        }

        /// <summary>
        /// 插屏次数是否已达上限
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        private bool IsInterCountReachUpperLimit(eAdInterstitial pos)
        {
            TryToResetState();
            int key = (int)pos;
            int count = _model.GetInterWatchCountValue(key);
            AdInterstitial cfg;
            AdInterstitialConfigs.TryGetValue(key, out cfg);
            long tempValue = cfg != null ? cfg.LimitPerDay : 0;
            return count >= tempValue;
        }

        public int GetRewardLastCount(eAdReward pos)
        {
            TryToResetState();
            int key = (int)pos;
            int count = _model.GetRewardWatchCount(key);
            AdReward cfg;
            AdRewardConfigs.TryGetValue(key, out cfg);
            long tempValue = cfg != null ? cfg.LimitPerDay : 0;
            return (int)(tempValue - count);
        }

        public bool ShouldShowRV(eAdReward pos, bool bi = true)
        {
            return SDK<IAdProvider>.Instance.ShouldShowRewardVideo(pos.ToString(), bi);
        }

        public bool ShouldShowInterstitial(eAdInterstitial pos, bool withBI = true)
        {
            return SDK<IAdProvider>.Instance.ShouldShowInterstitial(pos.ToString(), withBI);
        }

        public bool UnLockInterstitial(eAdInterstitial pos ,bool isEndlss = false)
        {
            
            //var commonSorage = SDK<IStorage>.Instance.Get<StorageCommon>();
            // int day =  TMUtility.GetTotalDays((ulong)TMUtility.GetTimeStamp()) - TMUtility.GetTotalDays(commonSorage.InstalledAt);
            // if (day < 1)
            // {
            //     return false;
            // }
            //
            
            //TODO 每天第一轮无尽，才解锁插屏广告
            if (StorageExtension.GameEndlssStorage.TodayPlayCount <= 1 && isEndlss)
            {
                return false;
            }

            int key = (int)pos;
            if (AdInterstitialConfigs.TryGetValue(key, out var conf))
            {
                if ( StorageExtension.GameBlockLevelStorage.CurLevelInfo.levelId + 1 >= conf.UnlockLevel
                     ||StorageExtension.GameEndlssStorage.CurFirstScore >= conf.UnlockScore
                   )
                {
                    return true;
                }
            }

            return false;
        }

        public bool TryShowRewardedVideo(eAdReward pos, Action<AdPlayResult, string> cb, string rewardItem = "")
        {
            if (!_isInit)
            {
                cb?.Invoke(AdPlayResult.Fail, "");
                Debug.Log($"[AD] TryShowRewardedVideo [0] : {pos}");
                return false;
            }

            if (pos == eAdReward.GameWinDoubleReward)
            {
                GameWinDoubleRewardShow = true;
            }
            string fromEvn = BIHelper.GetCurEnvForBI();
#if UNITY_EDITOR
            UIView_Notice.ViewData openData = new UIView_Notice.ViewData()
            {
                titleKey = "Debug播放广告",
                content = "是否播放广告",
                onMidBtn = () =>
                {
                    BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventRvShow, fromEvn, pos.ToString(),
                        rewardItem);

                    if (cb != null) _rewardCallBack = cb;
                    OnRewardPlay(pos);
                    _rewardCallBack?.Invoke(AdPlayResult.Success, "");
                },
                showMidBtn = true,
                onCloseBtn = () =>
                {
                    if (cb != null) _rewardCallBack = cb;
                    _rewardCallBack?.Invoke(AdPlayResult.Fail, "");
                },
                showCloseBtn = true,
            };
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, openData);
            return true;

#elif DEBUG || DEVELOPMENT_BUILD
            if (isOpenVideoAds)
            {
                UIView_Notice.ViewData openData = new UIView_Notice.ViewData()
                {
                    titleKey = "Debug播放广告",
                    content = "是否播放广告",
                    onMidBtn = () =>
                    {
                        BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventRvShow, fromEvn, pos.ToString(),
                            rewardItem);

                        if (cb != null) _rewardCallBack = cb;
                        OnRewardPlay(pos);
                        _rewardCallBack?.Invoke(AdPlayResult.Success, "");
                    },
                    showMidBtn = true,
                    onCloseBtn = () =>
                    {
                        if (cb != null) _rewardCallBack = cb;
                        _rewardCallBack?.Invoke(AdPlayResult.Fail, "");
                    },
                    showCloseBtn = true,
                };
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, openData);
                return true;
            }
            else
            {
                Debug.Log($"[AD] TryShowRewardedVideo [1] : {pos}");
                if (SDK<IAdProvider>.Instance.TryShowRewardVideo(pos.ToString(), (b, s) =>
                    {
                        Debug.Log($"[AD] TryShowRewardedVideo [4] : {pos}");
                        OnRewardPlay(pos);
                        _rewardCallBack?.Invoke(b, s);
                        GameGlobal.GetMgr<SoundMgr>().UnPauseAllSounds();
                        GameGlobal.GetMgr<SoundMgr>().UnPauseBGMusic();
                    }))
                {
                    BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventRvShow, fromEvn, pos.ToString(),
                            rewardItem);
                    Debug.Log($"[AD] TryShowRewardedVideo [2] : {pos}");
                    if (cb != null) _rewardCallBack = cb;
                    GameGlobal.GetMgr<SoundMgr>().PauseAllSounds();
                    GameGlobal.GetMgr<SoundMgr>().PauseBGMusic();
                    return true;
                }
                else
                {
                    cb?.Invoke(AdPlayResult.Fail, "");
                    GameGlobal.GetMod<ModTip>().ShowTip(CoreUtils.GetLocalization("adbox_no_reward"));
                }
            }
#else
            Debug.Log($"[AD] TryShowRewardedVideo [1] : {pos}");
            if (SDK<IAdProvider>.Instance.TryShowRewardVideo(pos.ToString(), (b, s) =>
                {
                    Debug.Log($"[AD] TryShowRewardedVideo [4] : {pos}");
                    OnRewardPlay(pos);
                    _rewardCallBack?.Invoke(b, s);
                    GameGlobal.GetMgr<SoundMgr>().UnPauseAllSounds();
                    GameGlobal.GetMgr<SoundMgr>().UnPauseBGMusic();
                }))
            {
                BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventRvShow, fromEvn, pos.ToString(),
                        rewardItem);
                Debug.Log($"[AD] TryShowRewardedVideo [2] : {pos}");
                if (cb != null) _rewardCallBack = cb;
                GameGlobal.GetMgr<SoundMgr>().PauseAllSounds();
                GameGlobal.GetMgr<SoundMgr>().PauseBGMusic();
                return true;
            }
            else
            {
                cb?.Invoke(AdPlayResult.Fail, "");
                GameGlobal.GetMod<ModTip>().ShowTip(CoreUtils.GetLocalization("adbox_no_reward"));
            }
#endif

            Debug.Log($"[AD] TryShowRewardedVideo [3] : {pos}");
            return false;
        }

        public void ResetGameWinDoubleAd()
        {
            GameWinDoubleRewardShow = false;
            CLog.Info($"======>:ResetGameWinDoubleAd  GameWinDoubleRewardShow:{GameWinDoubleRewardShow}");

        }

        public bool TryShowInterstitial(eAdInterstitial pos)
        {
            if (!_isInit)
            {
                Debug.Log($"[AD] TryShowInterstitial [0] : {pos}");
                return false;
            }

            CLog.Info($"======>:TryShowInterstitial  GameWinDoubleRewardShow:{GameWinDoubleRewardShow}");
            if (GameWinDoubleRewardShow)
            {
                return false;
            }
            string fromEvn = BIHelper.GetCurEnvForBI();

            BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventInterstitialShowOpportunity, fromEvn, pos.ToString());
            Debug.Log($"[AD] TryShowInterstitial [1] : {pos}");
            if (SDK<IAdProvider>.Instance.TryShowInterstitial(pos.ToString(), (b, s) =>
                {
                    BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventInterstitialShow, fromEvn, pos.ToString());
                    Debug.Log($"[AD] TryShowInterstitial [4] : {pos}");
                    GameGlobal.GetMgr<SoundMgr>().UnPauseAllSounds();
                    GameGlobal.GetMgr<SoundMgr>().UnPauseBGMusic();
                    PlayedInterstitalDurLogin = true;
                }))
            {
                Debug.Log($"[AD] TryShowInterstitial [2] : {pos}");
                GameGlobal.GetMgr<SoundMgr>().PauseAllSounds();
                GameGlobal.GetMgr<SoundMgr>().PauseBGMusic();
                OnInterPlay(pos);
                return true;
            }

            Debug.Log($"[AD] TryShowInterstitial [3] : {pos}");
            return false;
        }

        public bool IsRemoveAd()
        {
            if (SDK<IAP>.Instance.IsInitialized())
            {
                if (!_storageRemoveAd.RemoveAd)
                {
                    var iapItems = TMGame.GameGlobal.GetMod<IAPSys>().GetModal().GetNoAdShopInfo();
                    foreach (var p in iapItems)
                    {
                        if (p.GetIAPShopType() == ShopType.NoAd)
                        {
                            if (SDK<IAP>.Instance.HasOwnedProduct(p.Product_id))
                            {
                                _storageRemoveAd.RemoveAd = true;
                                _storageRemoveAd.RemoveBannerAd = true;
                                Debug.Log(
                                    $"restore removeAd : {p.Id}-{p.Product_id} from iapManager.HasOwnedProduct.");
                                break;
                            }
                        }
                    }
                }
            }

            return _storageRemoveAd.RemoveAd;
        }

        public void SetRemoveAd()
        {
            _storageRemoveAd.RemoveAd = true;
            _storageRemoveAd.RemoveBannerAd = true;
            Debug.Log("SetRemoveAd true");
        }

        public bool IsUnlock()
        {
            var unlockLevel = TMGame.GameGlobal.GetMod<IAPSys>().GetRemoveAdCfg().UnlockLevel;
            var mainLevel = 5;
            if (unlockLevel <= mainLevel)
            {
                return true;
            }

            return false;
        }

        public bool CanShow()
        {
            if (_storageRemoveAd.RemoveAd) return false;
            return IsUnlock();
        }

        public bool TryToAutoOpen()
        {
            // if (CanShow())
            // {
            //     var removeAdCfg = TMGame.GameGlobal.GetMod<IAPSys>().GetRemoveAdCfg();
            //     var lastPopupTime = _storageRemoveAd.LastPopupTimestamp;
            //     var now = (long)SDK<INetwork>.Instance.GetServerTime();
            //     var lastShowDays = TMUtility.GetTotalDays((ulong)lastPopupTime);
            //     var nowDays = TMUtility.GetTotalDays((ulong)now);
            //     var interval = (now - lastPopupTime) / 1000;
            //     if (nowDays != lastShowDays && interval > removeAdCfg.ShowInterval)
            //     {
            //         _storageRemoveAd.PopupDailyTimes = 1;
            //         _storageRemoveAd.LastPopupTimestamp = now;
            //         var purchaseId = removeAdCfg.RemoveAdsGiftId != null && removeAdCfg.RemoveAdsGiftId.Count > 1 ? removeAdCfg.RemoveAdsGiftId[1] : 101;
            //         //todo RemoveAd 弹窗 
            //         return true;
            //     }
            //     else if (nowDays == lastShowDays)
            //     {
            //         if (interval > removeAdCfg.ShowInterval &&
            //             _storageRemoveAd.PopupDailyTimes < removeAdCfg.LimitPerDay)
            //         {
            //             _storageRemoveAd.PopupDailyTimes++;
            //             _storageRemoveAd.LastPopupTimestamp = now;
            //             var purchaseId = removeAdCfg.RemoveAdsGiftId != null && removeAdCfg.RemoveAdsGiftId.Count > 1 ? removeAdCfg.RemoveAdsGiftId[1] : 101;
            //             //todo RemoveAd 弹窗  
            //             return true;
            //         }
            //     }
            //
            //     return false;
            // }

            return false;
        }

        public bool IsInterstitialReady(eAdInterstitial pos)
        {
            return SDK<IAdProvider>.Instance.IsInterstitialReady();
        }

        public bool IsRewardVideoReady(eAdReward pos)
        {
            return SDK<IAdProvider>.Instance.IsRewardVideoReady();
        }

        public void DebugResetRV()
        {
            _model.GetStorage().RewardWatchLastTimeStamp.Clear();
            _model.GetStorage().RewardWatchCount.Clear();
        }

        public void DebugResetInter()
        {
            _model.GetStorage().InterWatchLastTimeStamp.Clear();
            _model.GetStorage().InterWatchCount.Clear();
        }

        public string DebugGetRVInfo()
        {
            string infoStr = "\n";
            for (var key = 1; key <= 4; key++)
            {
                int count;
                _model.GetStorage().RewardWatchCount.TryGetValue(key, out count);
                AdReward cfg;
                AdRewardConfigs.TryGetValue(key, out cfg);
                if (cfg != null)
                {
                    infoStr += $"广告位:{key}, groupId:{cfg.GroupId}, {count}/{cfg.LimitPerDay}\n";
                }
            }
            return infoStr;
        }

        public string DebugGetInterInfo()
        {
            string infoStr = "\n";
            for (var key = 1; key <= 2; key++)
            {
                int count;
                _model.GetStorage().InterWatchCount.TryGetValue(key, out count);
                AdInterstitial cfg;
                AdInterstitialConfigs.TryGetValue(key, out cfg);
                if (cfg != null)
                {
                    infoStr += $"广告位:{key}, groupId:{cfg.GroupId}, {count}/{cfg.LimitPerDay}\n";
                }
            }
            return infoStr;
        }

        /// <summary>
        /// 获取展示的广告礼包ID 
        /// </summary>
        /// <param name="idx">0为没有金币的，1为有金币的</param>
        /// <returns></returns>
        public int GetRemoveAdPackShopId(int idx)
        {
            var removeAdCfg = TMGame.GameGlobal.GetMod<IAPSys>().GetRemoveAdCfg();
            if (idx == 0)
            {
                return removeAdCfg.RemoveAdsGiftId != null && removeAdCfg.RemoveAdsGiftId.Count > 0 ? removeAdCfg.RemoveAdsGiftId[0] : 100;
            }
            return removeAdCfg.RemoveAdsGiftId != null && removeAdCfg.RemoveAdsGiftId.Count > 1 ? removeAdCfg.RemoveAdsGiftId[1] : 101;
        }

        /// <summary>
        /// 获取 AdBanner 当前用户分层
        /// </summary>
        /// <returns></returns>
        private int GetAdBannerCurrentGroup()
        {
            return 100; //默认100
        }

        /// <summary>
        /// banner是否解锁
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        private bool IsBannerUnlock(eAdBanner pos)
        {
            int key = (int)pos;
            var theLevel = 5;
            return theLevel >= AdBannerConfigs[key].UnlockLevel;
        }

        public bool IsRemoveBannerAd()
        {
            if (_storageRemoveAd == null) return false;

            return _storageRemoveAd.RemoveBannerAd || IsRemoveAd();
        }

        public void TryShowCloseBanner()
        {
            // if (GameGlobal.GetMgr<UIManager>().GetWindow<UITripleMatchOut>() == null && ShowingBanner)
            // {
            //    TryShowBanner(eAdBanner.TMatch);
            // }
        }

        public bool TryShowBanner(eAdBanner pos)
        {
            if (!_isInit)
            {
                Debug.Log($"[AD] TryShowBanner [0] : {pos}");
                return false;
            }

#if UNITY_EDITOR
            _showingBanner = true;
            return true;
#else
            Debug.Log($"[AD] TryShowBanner : {pos}");
            if (SDK<IAdProvider>.Instance.TryShowBanner(pos.ToString()))
            {
                Debug.Log($"[AD] TryShowBanner success : {pos}");
                OnBannerPlay(pos);
                _showingBanner = true;
                return true;
            }
            Debug.Log($"[AD] TryShowBanner fail : {pos}");
            return false;
#endif
        }

        public void HideBanner()
        {
            _showingBanner = false;
#if UNITY_EDITOR
            return;
#endif
            SDK<IAdProvider>.Instance.HideBanner();
        }

        /// <summary>
        /// Banner播放前回调
        /// </summary>
        /// <param name="pos"></param>
        private void OnBannerPlay(eAdBanner pos)
        {
            //todo:
            int key = (int)pos;
            // if (!StorageAd.InterWatchCount.ContainsKey(key)) StorageAd.InterWatchCount.Add(key, 0);
            // StorageAd.InterWatchCount[key]++;
            //
            // var nowStamp = Utils.TotalSeconds();
            // if(!StorageAd.InterWatchLastTimeStamp.ContainsKey(key)) StorageAd.InterWatchLastTimeStamp.Add(key, nowStamp);
            // StorageAd.InterWatchLastTimeStamp[key] = nowStamp;
            //
            // interCommonCDStamp = 0;
        }
    }
}