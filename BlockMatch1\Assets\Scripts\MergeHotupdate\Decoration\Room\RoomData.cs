﻿using System;
using System.Collections.Generic;
using System.Linq;
using DragonPlus.Core;
using DragonPlus.Save;
using TMGame.Storage;
using RoomId = System.Int32;
using RoomNodeId = System.Int64;
using UnityEngine;

namespace DecorationRom.Core
{
    public class RoomData
    {
        private TableRoom _roomConfig;
        private List<TableRoomNode> _nodeConfigs;
        public string Sound_id => _roomConfig.soundId;

        public int AreaId;
        
        /**
         * 领奖励的间隔时间
         */
        public int RoomId { get => _roomConfig.id; }
        public string RoomName { get => _roomConfig?.roomName; }
        public TableRoom RoomConfig { get => _roomConfig; set => _roomConfig = value; }
        private Dictionary<int, TableRoomNode> _nodeConfigsDict = new Dictionary<RoomId, TableRoomNode>();

        private StorageRoom _storage;
        public RoomData(RoomId roomId, RoomId resId)
        {
            _roomConfig = TableConfigManage.Instance.GetTableRoom(roomId);
            _nodeConfigs = TableConfigManage.Instance.GetTableRoomNode(_roomConfig.id);

            _nodeConfigsDict.Clear();
            _nodeConfigs.ForEach(itemData =>
            {
                _nodeConfigsDict[itemData.id] = itemData;
                int resRoomNodeId = itemData.id % 100 + resId * 100;
                if (_nodeConfigsDict.ContainsKey(resRoomNodeId))
                    return;
                _nodeConfigsDict[resRoomNodeId] = itemData;
            });

            var storage = SDK<IStorage>.Instance.Get<StorageRoomCommon>();

            if (storage.RoomData.TryGetValue(_roomConfig.id, out var value))
            {
                _storage = value;
            }
            else
            {
                _storage = new StorageRoom()
                {
                    Id = _roomConfig.id,
                };
                storage.RoomData.Add(_roomConfig.id, _storage);
            }
        }

        public StorageNode GetNodeStorage(RoomNodeId nodeId)
        {
            if (_storage == null)
                return null;

            if (!_storage.RoomNodes.ContainsKey(nodeId))
                return null;

            return _storage.RoomNodes[nodeId];
        }

        public StorageDictionary<long, StorageNode> GetStorageNodes()
        {
            if (_storage == null)
                return null;

            return _storage.RoomNodes;
        }
        
        public void UnLockNextNode(RoomNodeId nodeId)
        {
            SetNodeStatus((int)nodeId, RoomItem.Status.Received);
            
            if (RoomManager.Instance.RoomChapter == 1001)
            {
                TableRoomNode nodeConfig = GetRoomNode((int)nodeId);
                if(nodeConfig == null)
                    return;

                if (nodeConfig.nextNode != null && nodeConfig.nextNode.Length > 0)
                {
                    if(nodeConfig.nextNode[0] > 0)
                    {
                        foreach (var nextId in nodeConfig.nextNode)
                            SetNodeStatus(nextId, RoomItem.Status.UnLock);
                    }
                    return;
                }  
            }

            foreach (var kv in _nodeConfigs)
            {
                StorageNode storageNode = GetNodeStorage(kv.id);
                if (storageNode == null)
                    continue;
                
                if(storageNode.Status == (int)RoomItem.Status.Received || storageNode.Status == (int)RoomItem.Status.UnLock)
                    continue;
                
                if(kv.preNode == null || kv.preNode.Length == 0 || kv.preNode[0] == 0)
                    continue;
                
                if(!kv.preNode.Contains((int)nodeId))
                    continue;

                bool isAllUnLock = true;
                for (int i = 0; i < kv.preNode.Length; i++)
                {
                    // 特殊条件(其实没用，兼容配置)
                    // -2.获得了家具以后才会出现挂点
                    // -3.由其他节点直接解锁
                    //  -6. 需要花费广告或者钻石等
                    if(kv.preNode[i] < 0) 
                        continue;
                    
                    storageNode = GetNodeStorage(kv.preNode[i]);
                    if (storageNode == null)
                    {
                        isAllUnLock = false;
                        break;
                    }

                    if (storageNode.Status != (int)RoomItem.Status.Received)
                    {
                        isAllUnLock = false;
                        break;
                    }
                }
                
                if(!isAllUnLock)
                    continue;

                SetNodeStatus(kv.id, RoomItem.Status.UnLock);
            }
        }

        private void SetNodeStatus(int id, RoomItem.Status status)
        {
            StorageNode node = null;
            if (_storage.RoomNodes.ContainsKey(id))
            {
                node = _storage.RoomNodes[id];
            }
            else
            {
                node = new StorageNode();
                node.Id = id;
                _storage.RoomNodes.Add(id, node);
            }
                
            node.Status = (int)status;
        }
        
        public void SetSelectId(RoomNodeId nodeId, string selectId)
        {
            StorageNode storageNode = GetNodeStorage(nodeId);
            if(storageNode == null)
                return;

            storageNode.SelectId = selectId;
        }
        
        public bool IsGetNode(RoomNodeId nodeId)
        {
            if (_storage == null)
                return false;

            if (!_storage.RoomNodes.ContainsKey(nodeId))
                return false;

            return true;
        }

        public void StorageRoomNode(RoomNodeId nodeId)
        {
            if (_storage == null)
                return;

            if (_storage.RoomNodes.ContainsKey(nodeId))
                return;
            
            _storage.RoomNodes.Add(nodeId, new StorageNode(){Id = nodeId});
        }

        public StorageRoom GetStorageRoom()
        {
            return _storage;
        }

        public TableRoomNode GetRoomNode(int nodeId)
        {
            if (_nodeConfigs == null || _nodeConfigsDict == null || _nodeConfigsDict.ContainsKey(nodeId) == false)
                return null;
            return _nodeConfigsDict[nodeId];
            //foreach (var kv in _nodeConfigs)
            //{
            //    if (kv.id == nodeId)
            //        return kv;
            //}

            //return null;
        }
        public RoomItem.Status GetNodeStatus(int nodeId)
        {
            if (!_storage.RoomNodes.ContainsKey(nodeId))
                return RoomItem.Status.Lock;

            return (RoomItem.Status)_storage.RoomNodes[nodeId].Status;
        }

        public string GetNodeSelectedItemId(int nodeId)
        {
            if (!_storage.RoomNodes.ContainsKey(nodeId))
                return null;

            return _storage.RoomNodes[nodeId].SelectId;
        }
    }
    
}