using System;
using System.Collections.Generic;
using DragonPlus;
using Framework.Wrapper;
using UnityEngine;
using UnityEngine.UI;
using RoomItemId = System.String;
using RoomNodeId = System.Int64;
using TMGame;
using DragonPlus.Core;
using Gameplay.BI;
using DragonPlus.Ad;
using Framework;
using UnityEngine.EventSystems;
using DecorationRom;
using static TMGame.FlySys;
using DecorationRom.Event;
using DragonU3DSDK.Network.API.Protocol;

public class UIItemGroup : UIGameObjectWrapper
{
    public enum Event
    {
        SelectionConfirm,
        SelectionCancel,
        CleanRoom,
        CloseSelectUI,
        OnClickBackToHome
    }

    public List<UISelectableItem> _uiItems = new List<UISelectableItem>();
    private RoomItem _selectItem;
    private TableRoomNode _nodeCfg;
    private Action<Event> _onEvent;
    private int _itemChangeTime = 0;
    private List<RoomItem> _itemList;

    private Button _btnCancel;
    public Button _btnOK;
    private Button _btnAd;
    private Button _btnAdNoFill;
    private Button _btnCollectJump;
    private Button _btnAdBottomLong; // 新增底部长条形ad按钮
    private Button _btnAdNoFillBottomLong; // 新增底部长条形ad按钮 没有填充
    private Button _btnUnlockLevel;
    const string GreenIcon = "ui_home_main_btnbg_yes";
    const string GrayIcon = "ui_home_main_btnbg_yes2";

    private Image keyImage;
    private LocalizeTextMeshProUGUI keyNum;
    private LocalizeTextMeshProUGUI buyTips;
    private LocalizeTextMeshProUGUI lockTips;
    private GameObject decorationGroup;
    public Vector3 FlyItemFromPos;
    private GameObject IconObj;

    public UIItemGroup(GameObject go, Action<Event> onEvent) : base(go)
    {
        _onEvent = onEvent;
        _btnCancel = GetItem<Button>("SelectFurnituresGroup/CloseSelectButton/UIBtn_CloseSelectButton");
        _btnOK = GetItem<Button>("SelectFurnituresGroup/SureSelectButton/UIBtn_SureSelectButton");
        _btnAd = GetItem<Button>("SelectFurnituresGroup/ADSButton/UIBtn_ADSButton");
        _btnUnlockLevel = GetItem<Button>("SelectFurnituresGroup/SureGoButton/UIBtn_SureGoButton");
        _btnAdBottomLong = GetItem<Button>("SelectFurnituresGroup/ADSButtonBottomLong/UIBtn_ADSButtonBottomLong");
        _btnAd.gameObject.SetActive(false);
        _btnAdBottomLong.gameObject.SetActive(false);
        _btnAdNoFill = GetItem<Button>("SelectFurnituresGroup/ADSLoadingButton/UIBtn_ADSLoadingButton");
        _btnAdNoFillBottomLong =
            GetItem<Button>("SelectFurnituresGroup/ADSLoadingButtonBottomLong/UIBtn_ADSLoadingButtonBottomLong");
        BindButton("SelectFurnituresGroup/SureSelectButton/UIBtn_SureSelectButton", onSelectionConfirmClick_BuildCoins);
        BindButton("SelectFurnituresGroup/CloseSelectButton/UIBtn_CloseSelectButton", OnSelectionCancelClick);
        _btnAd.onClick.AddListener(OnAdClick);
        _btnAdNoFill.onClick.AddListener(OnAdClick);
        _btnAdBottomLong.onClick.AddListener(OnAdClick);
        _btnAdNoFillBottomLong.onClick.AddListener(OnAdClick);
        _btnUnlockLevel.onClick.AddListener(OnClickUnLockLevel);
        decorationGroup = GetItem("DecorationGroup");
        keyImage = GetItem<Image>("DecorationGroup/DecorationIcon");
        keyNum = GetItem<LocalizeTextMeshProUGUI>("DecorationGroup/UITxt_RewardText");
        buyTips = GetItem<LocalizeTextMeshProUGUI>("DecorationGroup/UITxt_DecorationText");
        lockTips = GetItem<LocalizeTextMeshProUGUI>("DecBottomGroup/UITxt_DecorationText");
        IconObj = GetItem("DecorationGroup/DecorationIcon");
    }

    /// <summary>
    /// 重置所有按钮状态，避免节点切换时出现按钮重叠
    /// </summary>
    private void ResetAllButtonStates()
    {
        _btnOK.gameObject.SetActive(false);
        _btnUnlockLevel.gameObject.SetActive(false);
        _btnAd.gameObject.SetActive(false);
        _btnAdNoFill.gameObject.SetActive(false);
        _btnAdBottomLong.gameObject.SetActive(false);
        _btnAdNoFillBottomLong.gameObject.SetActive(false);
        _btnCollectJump?.gameObject.SetActive(false);
    }

    private void OnClickUnLockLevel()
    {
        //进入关卡选择UI
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_GameAdventure);
        OnSelectionCancelClick();
        _onEvent.Invoke(Event.OnClickBackToHome);

    }

    /// <summary>
    /// 点击广告按钮，播放广告解锁装修节点
    /// </summary>
    private void OnAdClick()
    {
        if (_nodeCfg == null || !_nodeCfg.isRvGet)
        {
            return;
        }

        GameGlobal.GetMgr<SoundMgr>().PlayButtonClick();

        var adSys = GameGlobal.GetMod<AdSys>();
        adSys.TryShowRewardedVideo(eAdReward.DecorationUnlock, (result, str) =>
        {
            if (result == AdPlayResult.Success)
            {
                // 广告播放成功，解锁装修节点
                UnlockDecorationNode();
            }
            else
            {
                // 广告播放失败，可以显示提示信息
                CLog.Warning($"装修节点广告播放失败: {_nodeCfg.id}, result: {result}");
            }
        }, $"RoomNode_{_nodeCfg.id}");
    }

    /// <summary>
    /// 解锁装修节点
    /// </summary>
    private void UnlockDecorationNode()
    {
        if (_nodeCfg == null)
        {
            return;
        }

        TableRoomNode nodeConfig = _nodeCfg;

        // 购买并解锁节点
        RoomManager.Instance.CurrentInRoom.BuyNode(nodeConfig.id);
        RoomManager.Instance.CurrentInRoom.Data.UnLockNextNode(nodeConfig.id);

        if (nodeConfig.isClear)
        {
            if (!nodeConfig.soundStr.IsEmptyString())
            {
                GameGlobal.GetMgr<SoundMgr>().PlaySfx(nodeConfig.soundStr);
            }
            else
            {
                PlayAudio(nodeConfig, nodeConfig.sound);
            }

            RoomManager.Instance.PlayCleanRoomAnim(nodeConfig, () =>
            {
                _onEvent.Invoke(Event.SelectionConfirm);
                ReplaceRoomNode();
            });
        }
        else
        {
            ReplaceRoomNode();
        }
    }

    public void SetData(TableRoomNode nodeCfg)
    {
        _nodeCfg = nodeCfg;
        _itemChangeTime = 0;
        _uiItems.Clear();
        var room = RoomManager.Instance.CurrentInRoom;
        _itemList = room.GetNode(nodeCfg.id).ItemListWithOutOld;

        var itemStorage = room.Data.GetNodeStorage(_nodeCfg.id);
        // var confItem = RoomManager.Instance.GetRoomKeyIcon(_nodeCfg.roomId);
        // CoreUtils.SetImg(keyImage, CoreUtils.GetSprite("CommonItemIconAtlas", confItem.Icon, keyImage.gameObject));

        var rvItem = GetItem($"SelectFurnituresGroup/UILayoutH_FurnituresLayout/FurnitureItem{3}");
        rvItem.gameObject.SetActive(false);

        for (int i = 0; i < 3; i++)
        {
            var itemObj = GetItem($"SelectFurnituresGroup/UILayoutH_FurnituresLayout/FurnitureItem{i}");
            // GameGlobal.GetMod<GuideSys>().RegisterTarget(GuideTargetType.FirstEnterGame, itemObj.transform, i.ToString());
            if (_itemList != null && i < _itemList.Count && !_nodeCfg.isClear)
            {
                var selectImage = "NoBadge/SelectedIndicator";
                var itemId = _itemList[i].Id;
                var item = new UISelectableItem(itemObj, "UIIconMask/IconTesture", itemStorage, selectImage, "",
                    () =>
                    {
                        SelectItem(itemId);
                        // var guide = GameGlobal.GetMod<GuideSys>();
                        // if (guide.GetCurGuideId() == "GUIDE_101")
                        // {
                        //     guide.FinishCurrent(GuideTargetType.FirstEnterGame);
                        // }
                    });
                item.ShowBadge(true, false, false);
                _uiItems.Add(item);
                if (_itemList[i].Icon != null)
                {
                    _uiItems[i].SetIcon(_itemList[i].Icon);
                }
                else if (string.IsNullOrEmpty(_nodeCfg.decoraResName) == false)
                {
                    _uiItems[i].SetIcon(_nodeCfg.decoraResName);
                }

                _uiItems[i].SetActive(true);
                _uiItems[i].ShowGuide(false);
                _btnCancel.interactable = true;
            }
            else
            {
                itemObj.SetActive(false);
            }
        }

        if (_nodeCfg.isClear)
        {
            rvItem.gameObject.SetActive(true);
            Image rvImage = rvItem.transform.Find("UIIconMask/IconTesture").GetComponent<Image>();
            if (rvImage != null && !RoomItemId.IsNullOrEmpty(_nodeCfg.decoraResName))
            {
                CoreUtils.SetImg(rvImage,
                    CoreUtils.GetSprite("RoomItemAtlas", _nodeCfg.decoraResName, rvImage.gameObject));
            }
        }

        if (!string.IsNullOrEmpty(itemStorage.SelectId))
        {
            SelectItem(itemStorage.SelectId, true);
        }

        if (string.IsNullOrEmpty(itemStorage.SelectId) && _itemList != null && _itemList.Count > 0)
        {
            SetOKBtnEnable(false);
            room.Data.SetSelectId(_nodeCfg.id, _itemList[0].Id);

            SelectItem(itemStorage.SelectId, true);
        }

        InitDecorationText();
    }

    private void InitDecorationText()
    {
        lockTips?.SetText("");
        _btnCollectJump?.gameObject.SetActive(false);

        // 重置所有按钮状态，避免节点切换时出现按钮重叠
        ResetAllButtonStates();

        RoomItem.Status status = RoomItem.Status.Lock;
        var room = RoomManager.Instance.CurrentInRoom;
        if (!_nodeCfg.isClear)
        {
            status = room.Data.GetNodeStatus((int)_selectItem.Node.Id);
            decorationGroup.gameObject.SetActive(status != RoomItem.Status.Received);
        }
        else
            decorationGroup.gameObject.SetActive(true);

        keyNum.SetText(_nodeCfg.unLockPrice.ToString());

        string tips = "";
        // if (!string.IsNullOrEmpty(_nodeCfg.nameKey))
        //     tips = CoreUtils.GetLocalization(_nodeCfg.nameKey);

        buyTips.SetText(tips);
        keyImage.gameObject.SetActive(true);
        keyNum.gameObject.SetActive(true);
        _btnOK.gameObject.SetActive(true);

        if (_selectItem != null)
            status = room.Data.GetNodeStatus((int)_selectItem.Node.Id);

        if (_nodeCfg.isUnlockLevel && status == RoomItem.Status.UnLock)
        {
            bool isUnlock = RoomManager.Instance.IsRoomNodeUnlockLevel(_nodeCfg.id);
            _btnOK.gameObject.SetActive(isUnlock);
            _btnUnlockLevel.gameObject.SetActive(!isUnlock);
            keyImage.gameObject.SetActive(false);
            keyNum.gameObject.SetActive(false);
            _btnAdBottomLong.gameObject.SetActive(false);
            _btnAdNoFillBottomLong.gameObject.SetActive(false);
            if (!isUnlock)
            {
                buyTips.SetText(CoreUtils.GetLocalization("UI_decoration_challenge_prompt"));
            }
        }
        else if (_nodeCfg.isRvGet && (status == RoomItem.Status.UnLock))
        {
            bool canRv = GameGlobal.GetMod<AdSys>().ShouldShowRV(eAdReward.DecorationUnlock);
            bool canAdBuy = RoomManager.Instance.CheckIsCanBuyNode(_nodeCfg, lockTips); // 是否是需要 看广告解锁
            bool isUnlockByOther = RoomManager.Instance.IsRoomNodeUnlockOther(_nodeCfg.id); // 是否是 每日奖励活动 获取的
            if (_nodeCfg.unLockResType <= 0 || _nodeCfg.unLockPrice <= 0)
            {
                _btnAd.gameObject.SetActive(canRv && canAdBuy && isUnlockByOther == false);
                _btnAdNoFill.gameObject.SetActive(!canRv && canAdBuy && isUnlockByOther == false);
                _btnOK.gameObject.SetActive(isUnlockByOther);
                _btnCollectJump?.gameObject.SetActive(canAdBuy == false && isUnlockByOther == false);
                keyImage.gameObject.SetActive(false);
                keyNum.gameObject.SetActive(false);
                _btnAdBottomLong.gameObject.SetActive(false);
                _btnAdNoFillBottomLong.gameObject.SetActive(false);
            }
            else // 此处新增 ，当 unLockResType > 0 并且 isRvGet = TRUE 时，需要同时可以 1.广告 和 2.装修币 解锁
            {
                _btnAd.gameObject.SetActive(false);
                _btnAdNoFill.gameObject.SetActive(false);
                var group = GameGlobal.GetMod<AdSys>().GetAdRewardCurrentGroup();
                _btnAdBottomLong.gameObject.SetActive(canRv && canAdBuy && isUnlockByOther == false && group == 100);
                _btnAdNoFillBottomLong.gameObject.SetActive(!canRv && canAdBuy && isUnlockByOther == false &&
                                                            group == 100);
                _btnOK.gameObject.SetActive(true);
                _btnCollectJump?.gameObject.SetActive(canAdBuy == false && isUnlockByOther == false);
                keyImage.gameObject.SetActive(true);
                keyNum.gameObject.SetActive(true);
            }
        }
        else
        {
            _btnUnlockLevel.gameObject.SetActive(false);
            _btnAd.gameObject.SetActive(false);
            _btnAdNoFill.gameObject.SetActive(false);
            _btnAdBottomLong.gameObject.SetActive(false);
            _btnAdNoFillBottomLong.gameObject.SetActive(false);
        }
    }

    public void onSelectionConfirmClick_AdOrOther()
    {
        GameGlobal.GetMgr<SoundMgr>().PlayButtonClick();
        // GameGlobal.GetMod<GuideSys>().FinishCurrent(GuideTargetType.NodeBuy);
        if (_nodeCfg.isClear)
        {
            RoomBI.SendClickNodeBI(_nodeCfg.id);
            OnNodeBuy();
            return;
        }

        if (_selectItem == null)
        {
            _onEvent?.Invoke(Event.SelectionCancel);
            return;
        }

        // AudioManager.Instance.PlaySound(204);
        var room = RoomManager.Instance.CurrentInRoom;
        var status = room.Data.GetNodeStatus((int)_selectItem.Node.Id);
        if (status == RoomItem.Status.Lock)
        {
            // UITipBoxController.Show(CoreUtils.GetLocalization("&key.UI_home_tap_lock_item"));
            return;
        }

        if (status == RoomItem.Status.UnLock)
        {
            OnNodeBuy();
        }
        else
        {
            ReplaceRoomNode();
        }
    }


    private void onSelectionConfirmClick_BuildCoins()
    {
        GameGlobal.GetMgr<SoundMgr>().PlayButtonClick();
        var guide = GameGlobal.GetMod<GuideSys>();
        if (guide.GetCurGuideId() == "GUIDE_102")
        {
            guide.FinishCurrent(GuideTargetType.ConfirmClear);
        }

        // if (guide.GetCurGuideId() == "GUIDE_402")
        // {
        //     guide.FinishCurrent(GuideTargetType.ItemSelectConfirm);
        // }
        GameUtils.SetEventSystemEnable(false);
        if (_nodeCfg.isClear)
        {
            RoomBI.SendClickNodeBI(_nodeCfg.id);
            OnNodeBuy_BuildCoins(); // 修改为单独只判断 装修币 的逻辑
            return;
        }

        if (_selectItem == null)
        {
            _onEvent?.Invoke(Event.SelectionCancel);
            return;
        }

        // var room = RoomManager.Instance.CurrentInRoom;
        // var status = room.Data.GetNodeStatus((int)_selectItem.Node.Id);
        var room = RoomManager.Instance.CurrentInRoom;
        var status = room.Data.GetNodeStatus((int)_selectItem.Node.Id);
        nodeInCurrentFinished = status == RoomItem.Status.Received;
        if (status == RoomItem.Status.Lock)
        {
            //UITipBoxController.Show(CoreUtils.GetLocalization("&key.UI_home_tap_lock_item"));
            return;
        }

        // BIHelper.SendDecorationInfo(BIHelper.EDecorationInfoType.ChangeNode, RoomManager.Instance.CurrentInRoom.Id, (int)_selectItem.Node.Id);
        if (status == RoomItem.Status.UnLock)
        {
            if (_nodeCfg.unLockResType <= 0 || _nodeCfg.unLockPrice <= 0)
            {
                OnNodeBuy();
            }
            else
            {
                OnNodeBuy_BuildCoins(); // 修改为单独只判断 装修币 的逻辑
            }
        }
        else
        {
            ReplaceRoomNode();
        }
    }

    private RoomItem tempRoomItem;

    public void OnUnlocknextRoom()
    {
        _onEvent?.Invoke(Event.SelectionConfirm);
        if (tempRoomItem != null)
            tempRoomItem?.Node?.SetChoiceNodes(true);
    }

    private void ReplaceRoomNode(bool isPlayAudio = true)
    {
        _onEvent.Invoke(Event.CloseSelectUI);
        var room = RoomManager.Instance.CurrentInRoom;
        var preSelectedItemId = room.Data.GetNodeSelectedItemId(_nodeCfg.id);
        tempRoomItem = _selectItem;
        // bool isFinishedNode =false;
        // if (tempRoomItem != null)
        // {
        //     isFinishedNode = RoomManager.Instance.IsRoomNodeUnlock((int)_selectItem.Node.Id);
        // }
        System.Action f_call = () =>
        {
            GameGlobal.GetMod<ModCoroutine>().StartCoroutine(RoomUtility.RoomDecorationBoxLogic((isFinish) =>
            {
                _onEvent?.Invoke(Event.SelectionConfirm);
                if (tempRoomItem != null)
                {
                    tempRoomItem?.Node?.SetChoiceNodes(true);
                }

                GameUtils.SetEventSystemEnable(true);
                EventBus.Dispatch<RoomAnimationEndEvent>(new RoomAnimationEndEvent(room.Id, isFinish,
                    nodeInCurrentFinished));
                //todo  检测引导， 第一次装修完成，跳转到主界面
            }, nodeInCurrentFinished));
        };
        if (_selectItem == null)
            return;
        if(_nodeCfg.isClear && _selectItem.Id == preSelectedItemId)
            return;
        if (isPlayAudio)
            PlayAudio();

        room.Data.SetSelectId(_nodeCfg.id, _selectItem.Id);
        if (!nodeInCurrentFinished)
        {
            RoomManager.Instance.LastNodeId = _nodeCfg.id;
        }

        RoomBI.SendDecoEvent_ChangeItem(_nodeCfg.roomId, _nodeCfg.id, _selectItem.Id);

        _selectItem.SetPlayEffect(true);
        _selectItem.Node.SetShowNodes(false);
        if (preSelectedItemId != _selectItem.Id)
        {
            _selectItem.OnChangeDifferentItem(f_call);
        }
        else
        {
            _selectItem.OnConfirm(f_call);
        }

        _selectItem?.SaveSelection();

        _selectItem = null;
    }

    private void OnSelectionCancelClick()
    {
        GameGlobal.GetMgr<SoundMgr>().PlayButtonClick();
        CloseSelectUI();
        _onEvent.Invoke(Event.SelectionCancel);
    }

    public void CloseSelectUI()
    {
        try
        {
            // if (GameGlobal.GetMod<GuideSys>().IsShowingGuide())
            //     return;

            if (_selectItem != null)
            {
                var room = RoomManager.Instance.CurrentInRoom;
                var status = room.Data.GetNodeStatus((int)_selectItem.Node.Id);
                if (status == RoomItem.Status.UnLock)
                {
                    room.Data.SetSelectId(_nodeCfg.id, "");
                }

                _selectItem?.Node?.SetChoiceNodes(true);
                _selectItem.OnDeSelect();
                _selectItem?.Node?.ResetDefault();
            }

            _onEvent?.Invoke(Event.SelectionCancel);
            _selectItem = null;
        }
        catch (Exception e)
        {
            CLog.Error(e);
        }
    }

    private void updateConfirmButton()
    {
        if (_selectItem == null)
        {
            CLog.Error("item is null");
            return;
        }

        SetOKBtnEnable(true);
    }

    void SetOKBtnEnable(bool enable)
    {
        _btnOK.interactable = enable;
    }

    public void SelectItem(RoomItemId itemId, bool onSelect = false)
    {
        _selectItem = _itemList.Find(c => c.Id == itemId);
        if (_selectItem == null)
            return;
        updateConfirmButton();
        GameGlobal.GetMgr<SoundMgr>().PlaySfx(203);
        _itemChangeTime++;
        for (int i = 0; i < _uiItems.Count; i++)
        {
            _uiItems[i].SetSelected(itemId == _itemList[i].Id);
        }
        if(_nodeCfg.isClear) return;
        
        var room = RoomManager.Instance.CurrentInRoom;
        room.SetItem((RoomNodeId)_nodeCfg.id, _selectItem.Id, false, false);
        _selectItem.Node.SetChoiceNodes(false);
        //if(onSelect)
        _selectItem.OnSelected();
        // DebugUtil.Log($"=========Room   {onSelect}");
        if (onSelect)
        {
            RoomManager.Instance.CurrentInRoom.FocusOn(_nodeCfg.id);
        }
    }


    /// <summary>
    /// 当点击 购买按钮 <广告，每日任务跳转> 时
    /// </summary>
    public void OnNodeBuy()
    {
        TableRoomNode nodeConfig = _nodeCfg;
        if (nodeConfig.isRvGet || nodeConfig.isUnlockLevel)
        {
            RoomManager.Instance.CurrentInRoom.BuyNode(nodeConfig.id);
            RoomManager.Instance.CurrentInRoom.Data.UnLockNextNode(nodeConfig.id);

            if (nodeConfig.isClear)
            {
                if (!nodeConfig.soundStr.IsEmptyString())
                {
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx(nodeConfig.soundStr);
                }
                else
                {
                    PlayAudio(nodeConfig, nodeConfig.sound);
                }

                RoomManager.Instance.PlayCleanRoomAnim(nodeConfig, () =>
                {
                    _onEvent.Invoke(Event.SelectionConfirm);
                    ReplaceRoomNode();
                });
            }
            else
            {
                ReplaceRoomNode();
            }

            return;
        }

        if (!GameGlobal.GetMod<ModBag>().CanAfford((EItemType)nodeConfig.unLockResType, nodeConfig.unLockPrice))
        {
            if (RoomManager.Instance.IsCollectRoom(nodeConfig.roomId))
            {
                // UIPopupNoCollectionController.ShowUI();
                return;
            }

            UIView_PopupNoMoney.ShowUI((code) =>
            {
                if (code == 1)
                {
                    _onEvent.Invoke(Event.OnClickBackToHome);
                }
            });
            OnSelectionCancelClick();
            return;
        }

        RoomBI.SendBuyNodeBI(nodeConfig.id);
        RoomBI.SendDecoEvent_purchase_style(nodeConfig.roomId, nodeConfig.id, nodeConfig.unLockPrice);
        // 消耗装修币并发BI
        GameGlobal.GetMod<ModBag>().ConsumeItem((EItemType)nodeConfig.unLockResType, nodeConfig.unLockPrice,
            new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.Star)
                { data1 = nodeConfig.roomId.ToString(), data2 = nodeConfig.id.ToString() });
        RoomManager.Instance.CurrentInRoom.BuyNode(nodeConfig.id);
        RoomManager.Instance.CurrentInRoom.Data.UnLockNextNode(nodeConfig.id);
        if (nodeConfig.isClear)
        {
            RoomBI.SendCleanNodeBI(nodeConfig.id);
            PlayAudio();
            _onEvent?.Invoke(Event.CloseSelectUI);
            RoomManager.Instance.PlayCleanRoomAnim(nodeConfig, () =>
            {
                RoomBI.SendCleanFinishNodeBI(nodeConfig.id);
                _onEvent?.Invoke(Event.SelectionConfirm);
                ReplaceRoomNode(false);
            });
        }
        else
        {
            ReplaceRoomNode();
        }
    }

    private bool nodeInCurrentFinished = false;

    /// <summary>
    /// 当点击 购买按钮 <装修币> 时
    /// </summary>
    public void OnNodeBuy_BuildCoins()
    {
        TableRoomNode nodeConfig = _nodeCfg;

        if (!GameGlobal.GetMod<ModBag>().CanAfford((EItemType)nodeConfig.unLockResType, nodeConfig.unLockPrice))
        {
            if (RoomManager.Instance.IsCollectRoom(nodeConfig.roomId))
            {
                // UIPopupNoCollectionController.ShowUI();
                return;
            }

            UIView_PopupNoMoney.ShowUI((code) =>
            {
                if (code == 1)
                {
                    _onEvent.Invoke(Event.OnClickBackToHome);
                }
            });
            OnSelectionCancelClick();
            return;
        }

        // BIHelper.SendDecorationInfo(BIHelper.EDecorationInfoType.BuyDecorationNode, RoomManager.Instance.CurRoomId, RoomManager.Instance.GetCurRoomNodeId());
        RoomBI.SendBuyNodeBI(nodeConfig.id);
        RoomBI.SendDecoEvent_purchase_style(nodeConfig.roomId, nodeConfig.id, nodeConfig.unLockPrice);
        GameGlobal.GetMod<ModBag>().ConsumeItem((EItemType)nodeConfig.unLockResType, nodeConfig.unLockPrice,
            new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.Star)
                { data1 = nodeConfig.roomId.ToString(), data2 = nodeConfig.id.ToString() });
        RoomManager.Instance.CurrentInRoom.BuyNode(nodeConfig.id);
        RoomManager.Instance.CurrentInRoom.Data.UnLockNextNode(nodeConfig.id);

        GameGlobal.GetMod<FlySys>().FlyItem(nodeConfig.unLockResType, nodeConfig.unLockPrice, FlyItemFromPos,
            this.IconObj.transform.position,
            () =>
            {
                BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventRoomDecoration,
                    nodeConfig.roomId.ToString(), nodeConfig.id.ToString(), nodeConfig.unLockPrice.ToString());
                if (nodeConfig.isClear)
                {
                    RoomBI.SendCleanNodeBI(nodeConfig.id);
                    PlayAudio();
                    _onEvent?.Invoke(Event.CloseSelectUI);
                    RoomManager.Instance.PlayCleanRoomAnim(nodeConfig, () =>
                    {
                        RoomBI.SendCleanFinishNodeBI(nodeConfig.id);
                        ReplaceRoomNode(false);
                        EventBus.Dispatch<RoomAnimationEndEvent>(new RoomAnimationEndEvent(nodeConfig.id));
                    });
                }
                else
                {
                    ReplaceRoomNode();
                }
            }, true, 0.15f, false, false);
    }

    public void PlayAudio()
    {
        if (_nodeCfg == null)
            return;

        if (_selectItem != null && _nodeCfg.oneTimeClean && _selectItem.CanOneTimeChoice)
        {
            PlayAudio(_nodeCfg, _nodeCfg.oneTimeSound);
        }
        else if (!_nodeCfg.soundStr.IsEmptyString())
        {
            GameGlobal.GetMgr<SoundMgr>().PlaySfx(_nodeCfg.soundStr);
        }
    }

    public void PlayAudio(TableRoomNode table, int soundId)
    {
        // 检查soundId是否有效，避免播放ID为0或无效的音效
        if (soundId <= 0)
        {
            return;
        }

        float time = RoomSound.PlaySound(soundId);
        if (time > 0)
        {
            RoomSound.PauseAllMusic();
            GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(time, () =>
            {
                if (table != null && table.starSfx > 0)
                {
                    time = RoomSound.PlaySound(table.starSfx);
                    if (time > 0)
                    {
                        GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(time,
                            () => { RoomSound.ResumeAllMusic(); }));
                    }
                    else
                    {
                        RoomSound.ResumeAllMusic();
                    }
                }
                else
                {
                    RoomSound.ResumeAllMusic();
                }
            }));
        }
    }
    //public void ShowHideNodeBuy(bool isShow)
    //{
    //    int num = UserData.Instance.GetRes(UserData.ResourceId.Key);
    //    keyNum.SetText(num.ToString());
    //}
}