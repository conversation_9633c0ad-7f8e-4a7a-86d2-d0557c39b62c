#if DEBUG || DEVELOPMENT_BUILD
using System.ComponentModel;
using DragonPlus.Core;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using TMGame;
using SRDebugger;
public partial class DebugOption
{
    public const string Group_Prop = "道具";

    [Category(Group_Prop)]
    [SROptions.DisplayName("参数1")]
    [SROptions.SortAttribute(1)]
    public string Prop_Param1
    {
        get { return prop_Param1; }
        set { prop_Param1 = value; }
    }

    private string prop_Param1;

    [Category(Group_Prop)]
    [SROptions.DisplayName("参数2")]
    [SROptions.SortAttribute(2)]
    public string Prop_Param2
    {
        get { return prop_Param2; }
        set { prop_Param2 = value; }
    }

    private string prop_Param2;

    [Category(Group_Prop)]
    [SROptions.DisplayName("加10000金币")]
    public void AddCoin100()
    {
        TMGame.GameGlobal.GetMod<UserProfileSys>().AddItem(EItemType.Coin, 10000,
            new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.Debug));
        EventBus.Dispatch(new EventCurrencyChange(EItemType.Coin,true,10000));
    }
    
    [Category(Group_Prop)]
    [SROptions.DisplayName("加10000钻石")]
    public void AddDiamond10000()
    {
        TMGame.GameGlobal.GetMod<UserProfileSys>().AddItem(EItemType.Diamond, 10000,
            new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.Debug));
        EventBus.Dispatch(new EventCurrencyChange(EItemType.Diamond,true,10000));
    }
    [Category(Group_Prop)]
    [SROptions.DisplayName("加10000装修币")]
    public void AddDecoration100()
    {
        TMGame.GameGlobal.GetMod<UserProfileSys>().AddItem(EItemType.Key, 10000,
            new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.Debug));
        EventBus.Dispatch(new EventCurrencyChange(EItemType.Key,false,10000));
    }
    
    [Category(Group_Prop)]
    [SROptions.DisplayName("加道具")]
    public void Common_AddItem()
    {
        if (!int.TryParse(prop_Param1, out int _itemType))
            return;
        if (!int.TryParse(prop_Param2, out int _count))
            return;
        if (_itemType <= 0)
            return;
        GameGlobal.GetMod<ModBag>().AddItem((EItemType)_itemType, _count,
            new BIHelper.ItemChangeReasonArgs()
            {
                reason = BiEventBlockMatch1.Types.ItemChangeReason.Debug,
                data1 = _itemType.ToString(),
                data2 = _count.ToString(),
            });
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
    }
}
#endif